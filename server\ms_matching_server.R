# MS2与MS1匹配功能后端服务
# 处理MS2-MS1峰匹配的后端逻辑

# 加载必要的脚本
safe_source("utils/ms_matching_algorithm.R")
safe_source("utils/path_manager.R")

# MS2-MS1匹配服务器逻辑
ms_matching_server <- function(input, output, session) {
  
  # 匹配状态响应式值
  matching_status <- reactiveVal("未开始")
  matching_progress <- reactiveVal(0)
  matching_results <- reactiveVal(NULL)
  
  # 输出匹配状态
  output$ms_matching_status <- renderText({
    matching_status()
  })
  
  # 输出匹配进度
  output$ms_matching_progress <- renderText({
    if (matching_progress() > 0) {
      paste0("进度: ", round(matching_progress(), 1), "%")
    } else {
      ""
    }
  })
  
  # 匹配结果表格
  output$ms_matching_result_table <- DT::renderDataTable({
    results <- matching_results()
    if (is.null(results) || is.null(results$file_statistics)) {
      return(data.frame(
        文件名 = character(0),
        总MS2数 = integer(0),
        已匹配数 = integer(0),
        匹配率 = character(0),
        stringsAsFactors = FALSE
      ))
    }
    
    stats <- results$file_statistics
    display_data <- data.frame(
      文件名 = basename(stats$file_name),
      总MS2数 = stats$total_ms2,
      已匹配数 = stats$matched_ms2,
      匹配率 = paste0(round(stats$matched_ms2 / stats$total_ms2 * 100, 1), "%"),
      stringsAsFactors = FALSE
    )
    
    display_data
  }, options = list(
    pageLength = 10,
    scrollX = TRUE,
    language = list(url = '//cdn.datatables.net/plug-ins/1.10.11/i18n/Chinese.json')
  ))
  
  # 开始MS2-MS1匹配
  observeEvent(input$start_ms_matching, {
    # 检查是否选择了项目
    if (is.null(input$selected_project) || input$selected_project == "") {
      showNotification("请先选择一个项目", type = "warning")
      return()
    }
    
    # 获取数据库路径
    project_path <- get_project_path(input$selected_project)
    db_path <- file.path(project_path, "results", "spectra.db")
    
    if (!file.exists(db_path)) {
      showNotification("数据库文件不存在，请先进行数据转换", type = "error")
      return()
    }
    
    # 重置状态
    matching_status("准备中...")
    matching_progress(0)
    matching_results(NULL)
    
    # 获取匹配参数
    ppm_tolerance <- input$ms_matching_ppm_tolerance
    if (is.null(ppm_tolerance) || is.na(ppm_tolerance) || ppm_tolerance <= 0) {
      ppm_tolerance <- 20  # 默认20ppm
    }
    
    # 在后台执行匹配
    future({
      # 定义进度回调函数
      progress_callback <- function(progress, matched_count, processed_count) {
        # 更新进度（这里需要使用session$sendCustomMessage或其他方式）
        # 由于在future中，直接更新reactive值可能有问题，这里先记录日志
        cat("匹配进度:", round(progress, 1), "%, 已匹配:", matched_count, ", 已处理:", processed_count, "\n")
      }
      
      # 执行匹配
      result <- match_ms2_to_ms1_peaks(
        db_path = db_path,
        ppm_tolerance = ppm_tolerance,
        batch_size = 1000,
        progress_callback = progress_callback
      )
      
      return(result)
      
    }) %...>% (function(result) {
      if (result$success) {
        matching_status(paste0("匹配完成 - 成功匹配 ", result$matched_count, " 个MS2扫描"))
        matching_progress(100)
        
        # 获取详细统计信息
        stats <- get_matching_statistics(db_path)
        matching_results(stats)
        
        showNotification(
          paste0("MS2-MS1匹配完成！匹配率: ", round(result$match_rate * 100, 1), "%"),
          type = "success",
          duration = 5
        )
      } else {
        matching_status(paste0("匹配失败: ", result$error))
        showNotification(paste0("匹配失败: ", result$error), type = "error")
      }
    }) %...!% (function(error) {
      matching_status(paste0("匹配出错: ", error$message))
      showNotification(paste0("匹配出错: ", error$message), type = "error")
    })
  })
  
  # 查看匹配统计
  observeEvent(input$view_ms_matching_stats, {
    if (is.null(input$selected_project) || input$selected_project == "") {
      showNotification("请先选择一个项目", type = "warning")
      return()
    }
    
    project_path <- get_project_path(input$selected_project)
    db_path <- file.path(project_path, "results", "spectra.db")
    
    if (!file.exists(db_path)) {
      showNotification("数据库文件不存在", type = "error")
      return()
    }
    
    tryCatch({
      stats <- get_matching_statistics(db_path)
      matching_results(stats)
      
      # 更新状态显示
      if (stats$total_ms2 > 0) {
        matching_status(paste0(
          "统计信息 - 总MS2: ", stats$total_ms2,
          ", 已匹配: ", stats$matched_ms2,
          ", 匹配率: ", round(stats$match_rate * 100, 1), "%"
        ))
      } else {
        matching_status("数据库中没有MS2数据")
      }
      
    }, error = function(e) {
      showNotification(paste0("获取统计信息失败: ", e$message), type = "error")
    })
  })
  
  # 重置匹配结果
  observeEvent(input$reset_ms_matching, {
    if (is.null(input$selected_project) || input$selected_project == "") {
      showNotification("请先选择一个项目", type = "warning")
      return()
    }
    
    # 确认对话框
    showModal(modalDialog(
      title = "确认重置",
      "这将清除所有MS2-MS1匹配结果，此操作不可撤销。确定要继续吗？",
      footer = tagList(
        modalButton("取消"),
        actionButton("confirm_reset_matching", "确认重置", class = "btn-danger")
      )
    ))
  })
  
  # 确认重置匹配结果
  observeEvent(input$confirm_reset_matching, {
    removeModal()
    
    project_path <- get_project_path(input$selected_project)
    db_path <- file.path(project_path, "results", "spectra.db")
    
    if (!file.exists(db_path)) {
      showNotification("数据库文件不存在", type = "error")
      return()
    }
    
    tryCatch({
      con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
      
      # 清除所有匹配结果
      DBI::dbExecute(con, "UPDATE ms2_spectra_data SET ms1_peak_id = NULL WHERE ms1_peak_id IS NOT NULL")
      
      DBI::dbDisconnect(con)
      
      # 重置状态
      matching_status("匹配结果已重置")
      matching_progress(0)
      matching_results(NULL)
      
      showNotification("MS2-MS1匹配结果已重置", type = "success")
      
    }, error = function(e) {
      if (exists("con") && DBI::dbIsValid(con)) {
        DBI::dbDisconnect(con)
      }
      showNotification(paste0("重置失败: ", e$message), type = "error")
    })
  })
  
  # 导出匹配结果
  output$download_ms_matching_results <- downloadHandler(
    filename = function() {
      paste0("ms2_ms1_matching_results_", Sys.Date(), ".csv")
    },
    content = function(file) {
      if (is.null(input$selected_project) || input$selected_project == "") {
        return()
      }
      
      project_path <- get_project_path(input$selected_project)
      db_path <- file.path(project_path, "results", "spectra.db")
      
      if (!file.exists(db_path)) {
        return()
      }
      
      tryCatch({
        con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
        
        # 查询匹配结果
        query <- "
          SELECT 
            d.file_name,
            m.spectrum_id as ms2_spectrum_id,
            m.scan_index as ms2_scan_index,
            m.rtime as ms2_rtime,
            m.precursor_mz,
            m.precursor_intensity,
            m.prec_scan_num,
            m.ms1_peak_id,
            p.mz as ms1_peak_mz,
            p.intensity as ms1_peak_intensity,
            s1.scan_index as ms1_scan_index,
            s1.rtime as ms1_rtime
          FROM ms2_spectra_data m
          JOIN data_files d ON m.file_id = d.file_id
          LEFT JOIN ms1_peaks_data p ON m.ms1_peak_id = p.peak_id
          LEFT JOIN ms1_spectra_data s1 ON p.spectrum_id = s1.spectrum_id
          WHERE m.ms1_peak_id IS NOT NULL
          ORDER BY d.file_name, m.rtime
        "
        
        results <- DBI::dbGetQuery(con, query)
        DBI::dbDisconnect(con)
        
        # 写入CSV文件
        write.csv(results, file, row.names = FALSE, fileEncoding = "UTF-8")
        
      }, error = function(e) {
        if (exists("con") && DBI::dbIsValid(con)) {
          DBI::dbDisconnect(con)
        }
        cat("导出匹配结果失败:", e$message, "\n")
      })
    }
  )
}
