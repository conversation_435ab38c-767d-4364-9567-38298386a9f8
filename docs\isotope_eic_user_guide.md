# 同位素内标EIC数据提取功能使用指南

## 功能概述

同位素内标EIC数据提取功能是质谱数据质控系统的重要组成部分，提供了从数据提取到统计分析再到可视化的完整解决方案。该功能帮助用户：

- 提取同位素内标离子的EIC（Extracted Ion Chromatogram）数据
- 分析EIC数据的统计指标（保留时间、mz偏差、峰型、半峰宽等）
- 可视化EIC数据和MS2碎片信息
- 识别和标记异常数据

## 使用前准备

### 1. 数据准备
- 确保项目中已有质谱数据库（spectra.db）
- 配置同位素内标化合物信息

### 2. 同位素内标配置
在"监控离子管理"模块中添加同位素内标：

1. 点击"添加离子"按钮
2. 填写化合物信息：
   - **化合物名称**：如"咖啡因-13C3"
   - **分子质量**：同位素标记后的分子质量
   - **保留时间**：理论保留时间
   - **离子化模式**：positive 或 negative
   - **离子质荷比**：目标m/z值
   - **离子类型**：[M+H]+ 或 [M-H]-
   - **备注**：标记为"同位素内标"

3. 保存配置

## 功能使用步骤

### 第一步：EIC数据提取

1. **设置提取参数**
   - **质量容差 (ppm)**：默认20 ppm，可根据仪器精度调整
   - **最小强度阈值**：默认1000，过滤低强度噪音

2. **开始提取**
   - 点击"开始提取EIC数据"按钮
   - 系统会自动识别已配置的同位素内标
   - 实时显示提取状态和进度

3. **查看结果**
   - 提取完成后查看"提取摘要"
   - 点击"查看提取结果"查看详细信息
   - 点击"下载JSON数据"保存原始数据

### 第二步：EIC数据统计分析

1. **执行统计分析**
   - 确保已完成EIC数据提取
   - 点击"开始统计分析"按钮
   - 系统自动计算各项统计指标

2. **查看统计结果**
   - 在"统计分析结果"表格中查看详细数据
   - 异常数据会以红色标签高亮显示
   - 支持表格排序和搜索

3. **下载报告**
   - 点击"下载统计报告"获取CSV格式报告
   - 报告包含所有统计指标和异常标记

### 第三步：EIC数据可视化

1. **3D散点图可视化**
   - 在"选择化合物"下拉框中选择要查看的化合物
   - 选择"全部"可同时显示所有化合物
   - 3D图展示：
     - X轴：保留时间
     - Y轴：强度（对数刻度）
     - Z轴：样本名称
     - 颜色：mz偏差程度
     - 形状：是否有MS2数据

2. **MS2碎片谱图**
   - 在"选择MS2谱图"下拉框中选择特定谱图
   - 查看碎片离子的m/z和强度信息
   - 显示前体离子信息（mz、强度、碰撞能等）

## 统计指标说明

### 基本指标
- **保留时间**：最高强度点的保留时间
- **最大强度**：峰顶强度值
- **实际mz**：最高强度点的m/z值
- **mz偏差 (ppm)**：实际mz与理论mz的偏差

### 峰形指标
- **mz范围**：有效数据点的m/z范围
- **峰型质量**：基于高斯拟合的峰形评估（优/良/中/差）
- **峰型评分**：数值化的峰形质量评分（0-1）
- **半峰宽**：基于高斯拟合计算的半峰宽

### 定量指标
- **峰面积**：使用梯形积分法计算的峰面积
- **信噪比**：峰顶强度与基线噪音的比值
- **数据点数**：总的EIC数据点数
- **有效点数**：过滤噪音后的有效数据点数

## 异常数据识别

系统自动识别以下异常情况：

1. **RT异常**：保留时间偏离正常范围（基于四分位距）
2. **mz偏差大**：质量偏差超过20 ppm
3. **峰型差**：峰形质量评估为"差"
4. **信噪比低**：信噪比小于10

异常数据在统计表格中以红色标签显示，便于快速识别问题样本。

## 性能优化说明

### 数据量限制
- MS2谱图显示数量限制为50个（可在代码中调整）
- 可视化同时显示最多6个MS2谱图
- 大数据集建议分批处理

### 参数调优建议
- **质量容差**：高分辨率仪器可设置为5-10 ppm
- **强度阈值**：根据样本浓度调整，避免过多噪音干扰
- **噪音阈值因子**：默认3倍标准差，可根据数据质量调整

## 常见问题解决

### 1. 提取结果为空
- 检查同位素内标配置是否正确
- 确认离子化模式与数据文件匹配
- 适当放宽质量容差或降低强度阈值

### 2. 统计分析失败
- 确保已完成EIC数据提取
- 检查提取的数据是否包含有效的EIC点
- 查看日志文件了解具体错误信息

### 3. 可视化显示异常
- 检查浏览器是否支持WebGL（3D图形需要）
- 尝试刷新页面或重新选择化合物
- 减少同时显示的数据量

### 4. 性能问题
- 限制同时处理的化合物数量
- 调整可视化参数减少数据点
- 考虑分批处理大型数据集

## 数据导出格式

### JSON格式（EIC原始数据）
```json
{
  "metadata": { /* 提取元信息 */ },
  "compounds": {
    "化合物名称": {
      "isotope_info": { /* 同位素信息 */ },
      "eic_data_by_file": { /* EIC数据 */ },
      "ms2_data_by_peak": { /* MS2数据 */ }
    }
  }
}
```

### CSV格式（统计报告）
包含所有统计指标的表格数据，可用Excel等软件打开进行进一步分析。

## 技术支持

如遇到问题，请：
1. 查看应用日志文件
2. 检查数据库连接状态
3. 验证同位素内标配置
4. 联系技术支持团队

## 更新日志

- **v1.0**：基础EIC数据提取功能
- **v2.0**：添加统计分析功能
- **v3.0**：添加可视化功能和性能优化
