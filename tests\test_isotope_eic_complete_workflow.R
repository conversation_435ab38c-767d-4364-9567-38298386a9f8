# 同位素内标EIC完整功能流程测试
# 测试从数据提取到统计分析再到可视化的完整工作流程

# 加载必要的脚本
safe_source("utils/isotope_eic_extractor.R")
safe_source("utils/eic_statistics_analyzer.R")
safe_source("utils/eic_visualization.R")
safe_source("utils/logger.R")

# 测试完整工作流程
test_complete_isotope_eic_workflow <- function() {
  cat("\n=== 同位素内标EIC完整功能流程测试 ===\n")
  
  total_tests <- 0
  passed_tests <- 0
  
  # 测试辅助函数
  test_assert <- function(condition, description) {
    if (condition) {
      cat("✓", description, "\n")
      return(TRUE)
    } else {
      cat("✗", description, "\n")
      return(FALSE)
    }
  }
  
  test_section <- function(title) {
    cat("\n--- ", title, " ---\n")
  }
  
  # 创建测试数据库路径
  test_db_path <- tempfile(fileext = ".db")
  
  tryCatch({
    test_section("1. 创建测试数据")
    
    # 创建简化的测试数据库
    conn <- DBI::dbConnect(RSQLite::SQLite(), test_db_path)
    
    # 创建表结构
    DBI::dbExecute(conn, "
      CREATE TABLE data_files (
        file_id TEXT PRIMARY KEY,
        file_name TEXT,
        sample_type TEXT,
        scan_mode TEXT
      )
    ")
    
    DBI::dbExecute(conn, "
      CREATE TABLE ms1_spectra_data (
        spectrum_id INTEGER PRIMARY KEY,
        file_id TEXT,
        rtime REAL,
        scan_index INTEGER
      )
    ")
    
    DBI::dbExecute(conn, "
      CREATE TABLE ms1_peaks_data (
        peak_id INTEGER PRIMARY KEY,
        spectrum_id INTEGER,
        mz REAL,
        intensity REAL
      )
    ")
    
    DBI::dbExecute(conn, "
      CREATE TABLE ms2_spectra_data (
        spectrum_id INTEGER PRIMARY KEY,
        file_id TEXT,
        ms1_peak_id INTEGER,
        rtime REAL,
        precursor_mz REAL,
        precursor_intensity REAL,
        collision_energy REAL
      )
    ")
    
    DBI::dbExecute(conn, "
      CREATE TABLE ms2_peaks_data (
        peak_id INTEGER PRIMARY KEY,
        spectrum_id INTEGER,
        mz REAL,
        intensity REAL
      )
    ")
    
    # 插入测试数据
    DBI::dbExecute(conn, "
      INSERT INTO data_files VALUES 
      ('file1', 'QC_001.rds', 'QC', 'positive'),
      ('file2', 'SAMPLE_001.rds', 'SAMPLE', 'positive')
    ")
    
    DBI::dbExecute(conn, "
      INSERT INTO ms1_spectra_data VALUES 
      (1, 'file1', 4.18, 100),
      (2, 'file1', 4.20, 101),
      (3, 'file2', 4.19, 102)
    ")
    
    DBI::dbExecute(conn, "
      INSERT INTO ms1_peaks_data VALUES 
      (1, 1, 198.0845, 125000),
      (2, 2, 198.0840, 130000),
      (3, 3, 198.0848, 120000)
    ")
    
    DBI::dbExecute(conn, "
      INSERT INTO ms2_spectra_data VALUES 
      (1, 'file1', 1, 4.19, 198.0845, 125000, 25.0),
      (2, 'file2', 3, 4.20, 198.0848, 120000, 25.0)
    ")
    
    DBI::dbExecute(conn, "
      INSERT INTO ms2_peaks_data VALUES 
      (1, 1, 140.0348, 85000),
      (2, 1, 110.0712, 45000),
      (3, 2, 140.0350, 80000),
      (4, 2, 110.0715, 42000)
    ")
    
    DBI::dbDisconnect(conn)
    
    total_tests <- total_tests + 1
    if (test_assert(file.exists(test_db_path), "测试数据库创建成功")) {
      passed_tests <- passed_tests + 1
    }
    
    test_section("2. EIC数据提取测试")
    
    # 创建EIC提取器
    total_tests <- total_tests + 1
    extractor <- create_eic_extractor(test_db_path, tolerance_ppm = 20, min_intensity = 1000)
    if (test_assert(!is.null(extractor), "EIC提取器创建成功")) {
      passed_tests <- passed_tests + 1
    }
    
    # 定义测试同位素内标
    isotope_ion <- list(
      化合物名称 = "咖啡因-13C3",
      分子质量 = 197.0769,
      保留时间 = 4.2,
      离子化模式 = "positive",
      离子质荷比 = 198.0842,
      离子类型 = "[M+H]+",
      备注 = "同位素内标"
    )
    
    # 提取EIC数据
    total_tests <- total_tests + 1
    eic_data <- extractor$extract_isotope_eic(isotope_ion)
    if (test_assert(!is.null(eic_data), "EIC数据提取成功")) {
      passed_tests <- passed_tests + 1
    }
    
    # 转换为前端格式
    total_tests <- total_tests + 1
    frontend_data <- extractor$format_eic_data_for_frontend(eic_data)
    if (test_assert(!is.null(frontend_data), "EIC数据格式转换成功")) {
      passed_tests <- passed_tests + 1
    }
    
    test_section("3. EIC统计分析测试")
    
    # 创建统计分析器
    total_tests <- total_tests + 1
    analyzer <- create_eic_statistics_analyzer()
    if (test_assert(!is.null(analyzer), "EIC统计分析器创建成功")) {
      passed_tests <- passed_tests + 1
    }
    
    # 执行统计分析
    total_tests <- total_tests + 1
    json_data <- list(compounds = list())
    json_data$compounds[[isotope_ion$化合物名称]] <- frontend_data
    
    statistics_report <- analyze_eic_statistics_from_json(json_data)
    if (test_assert(!is.null(statistics_report) && nrow(statistics_report) > 0, "EIC统计分析执行成功")) {
      passed_tests <- passed_tests + 1
    }
    
    # 验证统计报告内容
    if (!is.null(statistics_report) && nrow(statistics_report) > 0) {
      total_tests <- total_tests + 1
      required_columns <- c("化合物名称", "样本文件", "保留时间", "mz偏差_ppm", "峰型质量", "信噪比")
      has_required_columns <- all(required_columns %in% names(statistics_report))
      if (test_assert(has_required_columns, "统计报告包含必要列")) {
        passed_tests <- passed_tests + 1
      }
    }
    
    test_section("4. EIC可视化测试")
    
    # 创建可视化器
    total_tests <- total_tests + 1
    visualizer <- create_eic_visualizer(max_ms2_spectra = 10)
    if (test_assert(!is.null(visualizer), "EIC可视化器创建成功")) {
      passed_tests <- passed_tests + 1
    }
    
    # 准备3D散点图数据
    total_tests <- total_tests + 1
    plot_data <- visualizer$prepare_3d_scatter_data(json_data)
    if (test_assert(!is.null(plot_data) && nrow(plot_data) > 0, "3D散点图数据准备成功")) {
      passed_tests <- passed_tests + 1
    }
    
    # 准备MS2碎片数据
    total_tests <- total_tests + 1
    ms2_data <- visualizer$prepare_ms2_fragment_data(json_data)
    if (test_assert(!is.null(ms2_data) && nrow(ms2_data) > 0, "MS2碎片数据准备成功")) {
      passed_tests <- passed_tests + 1
    }
    
    test_section("5. 完整工作流程验证")
    
    # 验证数据流的完整性
    total_tests <- total_tests + 1
    workflow_complete <- !is.null(eic_data) && 
                       !is.null(frontend_data) && 
                       !is.null(statistics_report) && 
                       !is.null(plot_data) && 
                       !is.null(ms2_data)
    
    if (test_assert(workflow_complete, "完整工作流程验证成功")) {
      passed_tests <- passed_tests + 1
    }
    
    # 验证数据一致性
    total_tests <- total_tests + 1
    compound_name_consistent <- all(
      statistics_report$化合物名称 == isotope_ion$化合物名称,
      plot_data$compound_name == isotope_ion$化合物名称,
      ms2_data$compound_name == isotope_ion$化合物名称
    )
    
    if (test_assert(compound_name_consistent, "数据一致性验证成功")) {
      passed_tests <- passed_tests + 1
    }
    
    # 清理测试文件
    if (file.exists(test_db_path)) {
      file.remove(test_db_path)
    }
    
    # 输出测试结果
    cat("\n=== 测试结果汇总 ===\n")
    cat("总测试数:", total_tests, "\n")
    cat("通过测试:", passed_tests, "\n")
    cat("失败测试:", total_tests - passed_tests, "\n")
    cat("通过率:", round(passed_tests / total_tests * 100, 1), "%\n")
    
    if (passed_tests == total_tests) {
      cat("\n🎉 所有测试通过！同位素内标EIC完整功能流程正常工作。\n")
      return(TRUE)
    } else {
      cat("\n⚠️  部分测试失败，请检查相关功能。\n")
      return(FALSE)
    }
    
  }, error = function(e) {
    cat("测试过程中发生错误:", e$message, "\n")
    
    # 清理测试文件
    if (file.exists(test_db_path)) {
      file.remove(test_db_path)
    }
    
    return(FALSE)
  })
}

# 运行测试（如果直接执行此脚本）
if (!interactive()) {
  test_complete_isotope_eic_workflow()
}
