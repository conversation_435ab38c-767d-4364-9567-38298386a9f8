# 同位素内标EIC数据提取功能测试
# 测试IsotopeEICExtractor类的各项功能

# 加载必要的包和脚本
source("utils/isotope_eic_extractor.R")
source("utils/logger.R")
source("utils/path_manager.R")

# 测试辅助函数
test_assert <- function(condition, message) {
  if (condition) {
    cat("✓ PASS:", message, "\n")
    return(TRUE)
  } else {
    cat("✗ FAIL:", message, "\n")
    return(FALSE)
  }
}

test_section <- function(title) {
  cat("\n", paste(rep("=", 50), collapse = ""), "\n")
  cat("测试部分:", title, "\n")
  cat(paste(rep("=", 50), collapse = ""), "\n")
}

# 创建测试数据库（模拟数据）
create_test_database <- function(db_path) {
  if (file.exists(db_path)) {
    file.remove(db_path)
  }
  
  conn <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  # 创建表结构
  DBI::dbExecute(conn, "
    CREATE TABLE data_files (
      file_id TEXT PRIMARY KEY,
      file_name TEXT,
      sample_type TEXT,
      scan_mode TEXT,
      polarity INTEGER,
      created_time TEXT
    )
  ")
  
  DBI::dbExecute(conn, "
    CREATE TABLE ms1_spectra_data (
      spectrum_id INTEGER PRIMARY KEY,
      file_id TEXT,
      scan_index INTEGER,
      rtime REAL,
      polarity INTEGER,
      peaks_count INTEGER,
      tot_ion_current REAL,
      base_peak_mz REAL,
      base_peak_intensity REAL,
      FOREIGN KEY (file_id) REFERENCES data_files(file_id)
    )
  ")
  
  DBI::dbExecute(conn, "
    CREATE TABLE ms1_peaks_data (
      peak_id INTEGER PRIMARY KEY,
      spectrum_id INTEGER,
      file_id TEXT,
      mz REAL,
      intensity REAL,
      scan_index INTEGER,
      rtime REAL,
      FOREIGN KEY (spectrum_id) REFERENCES ms1_spectra_data(spectrum_id),
      FOREIGN KEY (file_id) REFERENCES data_files(file_id)
    )
  ")
  
  DBI::dbExecute(conn, "
    CREATE TABLE ms2_spectra_data (
      spectrum_id INTEGER PRIMARY KEY,
      file_id TEXT,
      scan_index INTEGER,
      rtime REAL,
      prec_scan_num INTEGER,
      precursor_mz REAL,
      precursor_intensity REAL,
      precursor_charge INTEGER,
      collision_energy REAL,
      isolation_window_lower_mz REAL,
      isolation_window_target_mz REAL,
      isolation_window_upper_mz REAL,
      ms1_peak_id INTEGER,
      peaks_count INTEGER,
      tot_ion_current REAL,
      base_peak_mz REAL,
      base_peak_intensity REAL,
      FOREIGN KEY (file_id) REFERENCES data_files(file_id),
      FOREIGN KEY (ms1_peak_id) REFERENCES ms1_peaks_data(peak_id)
    )
  ")
  
  DBI::dbExecute(conn, "
    CREATE TABLE ms2_peaks_data (
      peak_id INTEGER PRIMARY KEY,
      spectrum_id INTEGER,
      mz REAL,
      intensity REAL,
      FOREIGN KEY (spectrum_id) REFERENCES ms2_spectra_data(spectrum_id)
    )
  ")
  
  # 插入测试数据
  # 文件数据
  DBI::dbExecute(conn, "
    INSERT INTO data_files VALUES 
    ('file_001', 'QC_001.rds', 'QC', 'positive', 1, '2025-08-01 10:00:00'),
    ('file_002', 'SAMPLE_001.rds', 'SAMPLE', 'positive', 1, '2025-08-01 10:30:00'),
    ('file_003', 'BLANK_001.rds', 'BLANK', 'negative', 0, '2025-08-01 11:00:00')
  ")
  
  # MS1谱图数据
  DBI::dbExecute(conn, "
    INSERT INTO ms1_spectra_data VALUES 
    (1, 'file_001', 100, 4.18, 1, 500, 1000000, 198.0845, 125000),
    (2, 'file_001', 101, 4.19, 1, 520, 1100000, 198.0840, 135000),
    (3, 'file_002', 105, 4.22, 1, 480, 950000, 198.0844, 98000)
  ")
  
  # MS1峰数据（咖啡因-13C3的测试数据）
  DBI::dbExecute(conn, "
    INSERT INTO ms1_peaks_data VALUES 
    (1, 1, 'file_001', 198.0845, 125000, 100, 4.18),
    (2, 2, 'file_001', 198.0840, 135000, 101, 4.19),
    (3, 3, 'file_002', 198.0844, 98000, 105, 4.22)
  ")
  
  # MS2谱图数据
  DBI::dbExecute(conn, "
    INSERT INTO ms2_spectra_data VALUES 
    (1, 'file_001', 102, 4.19, 101, 198.0840, 135000, 1, 25.0, 197.5, 198.0, 198.5, 2, 3, 200000, 140.0348, 85000),
    (2, 'file_002', 106, 4.22, 105, 198.0844, 98000, 1, 30.0, 197.5, 198.0, 198.5, 3, 2, 150000, 140.0350, 72000)
  ")
  
  # MS2碎片数据
  DBI::dbExecute(conn, "
    INSERT INTO ms2_peaks_data VALUES 
    (1, 1, 140.0348, 85000),
    (2, 1, 110.0712, 45000),
    (3, 1, 82.0293, 25000),
    (4, 2, 140.0350, 72000),
    (5, 2, 110.0715, 38000)
  ")
  
  DBI::dbDisconnect(conn)
  cat("测试数据库创建完成:", db_path, "\n")
}

# 主测试函数
run_isotope_eic_tests <- function() {
  cat("开始同位素内标EIC数据提取功能测试\n")
  cat("测试时间:", Sys.time(), "\n")
  
  # 创建临时测试目录
  test_dir <- file.path(tempdir(), "isotope_eic_test")
  if (!dir.exists(test_dir)) {
    dir.create(test_dir, recursive = TRUE)
  }
  
  test_db_path <- file.path(test_dir, "test_spectra.db")
  
  # 测试计数器
  total_tests <- 0
  passed_tests <- 0
  
  tryCatch({
    # 创建测试数据库
    create_test_database(test_db_path)
    
    test_section("1. EIC提取器初始化测试")
    
    # 测试1: 创建EIC提取器
    total_tests <- total_tests + 1
    extractor <- create_eic_extractor(test_db_path, tolerance_ppm = 20, min_intensity = 1000)
    if (test_assert(!is.null(extractor), "EIC提取器创建成功")) {
      passed_tests <- passed_tests + 1
    }
    
    # 测试2: 数据库连接
    total_tests <- total_tests + 1
    if (test_assert(!is.null(extractor$db_connection), "数据库连接成功")) {
      passed_tests <- passed_tests + 1
    }
    
    test_section("2. 文件匹配测试")
    
    # 测试3: 根据扫描模式匹配文件
    total_tests <- total_tests + 1
    matched_files <- extractor$match_files_by_scan_mode("positive", "[M+H]+")
    if (test_assert(nrow(matched_files) == 2, "正离子模式文件匹配正确")) {
      passed_tests <- passed_tests + 1
    }
    
    # 测试4: 负离子模式匹配
    total_tests <- total_tests + 1
    matched_files_neg <- extractor$match_files_by_scan_mode("negative", "[M-H]-")
    if (test_assert(nrow(matched_files_neg) == 1, "负离子模式文件匹配正确")) {
      passed_tests <- passed_tests + 1
    }
    
    test_section("3. MS1峰匹配测试")
    
    # 测试5: 根据m/z匹配MS1峰
    total_tests <- total_tests + 1
    target_mz <- 198.0842  # 咖啡因-13C3的理论m/z
    file_ids <- matched_files$file_id
    ms1_peaks <- extractor$match_ms1_peaks_by_mz(target_mz, file_ids)
    if (test_assert(nrow(ms1_peaks) == 3, "MS1峰匹配数量正确")) {
      passed_tests <- passed_tests + 1
    }
    
    # 测试6: 质量偏差计算
    total_tests <- total_tests + 1
    if (nrow(ms1_peaks) > 0) {
      max_error <- max(abs(ms1_peaks$mz_error_ppm))
      if (test_assert(max_error <= 20, paste("质量偏差在容差范围内 (最大偏差:", round(max_error, 2), "ppm)"))) {
        passed_tests <- passed_tests + 1
      }
    }
    
    test_section("4. MS2数据查找测试")
    
    # 测试7: 根据MS1峰ID查找MS2数据
    total_tests <- total_tests + 1
    if (nrow(ms1_peaks) > 0) {
      peak_ids <- ms1_peaks$peak_id
      ms2_spectra <- extractor$find_ms2_by_ms1_peak_ids(peak_ids)
      if (test_assert(nrow(ms2_spectra) == 2, "MS2谱图查找正确")) {
        passed_tests <- passed_tests + 1
      }
      
      # 测试8: MS2碎片数据查找
      total_tests <- total_tests + 1
      if (nrow(ms2_spectra) > 0) {
        ms2_spectrum_ids <- ms2_spectra$spectrum_id
        ms2_fragments <- extractor$find_ms2_fragments(ms2_spectrum_ids)
        if (test_assert(nrow(ms2_fragments) == 5, "MS2碎片数据查找正确")) {
          passed_tests <- passed_tests + 1
        }
      }
    }
    
    test_section("5. 完整EIC数据提取测试")
    
    # 测试9: 单个同位素内标EIC提取
    total_tests <- total_tests + 1
    isotope_ion <- list(
      化合物名称 = "咖啡因-13C3",
      分子质量 = 197.0769,
      保留时间 = 4.2,
      离子化模式 = "positive",
      离子质荷比 = 198.0842,
      离子类型 = "[M+H]+",
      备注 = "同位素内标"
    )
    
    eic_data <- extractor$extract_isotope_eic(isotope_ion)
    if (test_assert(!is.null(eic_data), "单个同位素内标EIC提取成功")) {
      passed_tests <- passed_tests + 1
    }
    
    # 测试10: EIC数据结构验证
    total_tests <- total_tests + 1
    if (!is.null(eic_data)) {
      required_fields <- c("isotope_info", "matched_files", "ms1_peaks", "ms2_spectra", "ms2_fragments")
      has_all_fields <- all(required_fields %in% names(eic_data))
      if (test_assert(has_all_fields, "EIC数据结构完整")) {
        passed_tests <- passed_tests + 1
      }
    }
    
    test_section("6. JSON格式转换测试")
    
    # 测试11: 前端JSON格式转换
    total_tests <- total_tests + 1
    if (!is.null(eic_data)) {
      frontend_data <- extractor$format_eic_data_for_frontend(eic_data)
      if (test_assert(!is.null(frontend_data), "前端JSON格式转换成功")) {
        passed_tests <- passed_tests + 1
      }
      
      # 测试12: JSON结构验证
      total_tests <- total_tests + 1
      if (!is.null(frontend_data)) {
        required_json_fields <- c("isotope_info", "eic_data_by_file", "ms2_data_by_peak", "summary")
        has_json_fields <- all(required_json_fields %in% names(frontend_data))
        if (test_assert(has_json_fields, "前端JSON结构完整")) {
          passed_tests <- passed_tests + 1
        }
      }
    }
    
    # 断开数据库连接
    extractor$disconnect_database()
    
  }, error = function(e) {
    cat("测试过程中发生错误:", e$message, "\n")
  }, finally = {
    # 清理测试文件
    if (file.exists(test_db_path)) {
      file.remove(test_db_path)
    }
    if (dir.exists(test_dir)) {
      unlink(test_dir, recursive = TRUE)
    }
  })
  
  # 输出测试结果
  test_section("测试结果汇总")
  cat("总测试数:", total_tests, "\n")
  cat("通过测试:", passed_tests, "\n")
  cat("失败测试:", total_tests - passed_tests, "\n")
  cat("通过率:", round(passed_tests / total_tests * 100, 1), "%\n")
  
  if (passed_tests == total_tests) {
    cat("🎉 所有测试通过！\n")
    return(TRUE)
  } else {
    cat("❌ 部分测试失败，请检查代码\n")
    return(FALSE)
  }
}

# 运行测试（如果直接执行此脚本）
if (!interactive()) {
  run_isotope_eic_tests()
}
