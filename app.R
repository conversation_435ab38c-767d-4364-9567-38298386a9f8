# 实验室实时质控系统 - 主应用入口
# 单项目模式，支持多开

# 设置软件安装路径（在加载global.R之前）
# 确保APP_ROOT_PATH在全局环境中正确设置
APP_ROOT_PATH <- normalizePath(getwd())
assign("APP_ROOT_PATH", APP_ROOT_PATH, envir = .GlobalEnv)

# 加载全局配置
source("global.R")  # global.R必须在path_manager.R加载前执行，不能使用safe_source

# 加载必要的包
library(shiny)
library(DT)
library(plotly)
library(dplyr)
library(fs)
library(shinyjs)
library(shinyFiles)
library(yaml)

# 加载自定义模块和工具函数
safe_source("utils/config_manager.R")
safe_source("utils/logger.R")
safe_source("utils/error_handler.R")

# 加载UI模块
safe_source("ui/main_ui.R")

# 加载Server模块
safe_source("server/main_server.R")

# 加载功能模块
# source("modules/data_adapters/spectra_adapter.R")  # 已废弃，实际未用到，安全删除
# source("modules/monitors/monitor_ions_config.R")  # 已废弃，使用utils/monitor_ions_manager.R替代

# 启动Shiny应用
shinyApp(ui = ui, server = server, options = list(
  port = 3839,  # 使用不同端口避免冲突
  host = "127.0.0.1",
  # 增加文件上传大小限制到1GB
  shiny.maxRequestSize = 1024 * 1024^2
))