# MS2-MS1峰匹配功能使用说明

## 功能概述

MS2-MS1峰匹配功能用于建立MS2扫描与对应MS1峰之间的关联关系。该功能通过分析前体离子的质荷比、扫描编号等信息，将MS2扫描与最匹配的MS1峰进行关联，为后续的质谱数据分析提供重要的结构信息。

## 匹配逻辑

### 匹配步骤

1. **MS1扫描查找**：根据MS2扫描的`file_id`和`prec_scan_num`字段，在`ms1_spectra_data`表中查找对应的MS1扫描数据
2. **峰候选筛选**：在找到的MS1扫描中，基于`spectrum_id`在`ms1_peaks_data`表中查找所有峰
3. **质量匹配**：筛选出与`precursor_mz`的ppm误差在指定容差范围内的峰
4. **最佳匹配选择**：如果有多个候选峰，基于综合得分选择最佳匹配

### 匹配算法

- **质量容差**：默认20ppm，可调整
- **综合得分计算**：
  - ppm误差权重：60%
  - 强度相似度权重：40%
- **强度相似度**：基于对数比值计算，避免强度差异过大的影响

## 数据库结构变更

### 新增字段

在`ms2_spectra_data`表中添加了`ms1_peak_id`字段：

```sql
ALTER TABLE ms2_spectra_data ADD COLUMN ms1_peak_id INTEGER;
```

### 外键约束

```sql
FOREIGN KEY (ms1_peak_id) REFERENCES ms1_peaks_data(peak_id) ON DELETE SET NULL
```

### 新增索引

```sql
CREATE INDEX idx_ms2_spectra_ms1_peak_id ON ms2_spectra_data(ms1_peak_id);
CREATE INDEX idx_ms2_spectra_file_prec_scan ON ms2_spectra_data(file_id, prec_scan_num);
```

## 使用方法

### 1. 通过UI界面使用

1. 打开应用程序，进入工作区页面
2. 在"同位素内标监控"模块中找到"MS2-MS1峰匹配"子区域
3. 设置匹配参数：
   - **质量容差 (ppm)**：默认20ppm，可根据仪器精度调整
4. 点击"开始匹配"按钮启动匹配过程
5. 查看匹配进度和结果统计
6. 可以导出匹配结果为CSV文件

### 2. 通过R脚本使用

```r
# 加载匹配算法
source("utils/ms_matching_algorithm.R")

# 执行匹配
result <- match_ms2_to_ms1_peaks(
  db_path = "path/to/spectra.db",
  ppm_tolerance = 20,
  batch_size = 1000
)

# 查看结果
if (result$success) {
  cat("匹配成功，匹配率:", round(result$match_rate * 100, 1), "%\n")
  
  # 获取详细统计
  stats <- get_matching_statistics("path/to/spectra.db")
  print(stats)
}
```

### 3. 数据库升级

如果使用现有数据库，需要先升级数据库结构：

```r
# 加载升级脚本
source("utils/database_upgrade.R")

# 安全升级（包含备份）
success <- safe_upgrade_database("path/to/spectra.db")
```

## 功能特点

### 高性能设计

- **批量处理**：支持大批量数据的高效处理
- **内存优化**：使用data.table提高数据处理性能
- **索引优化**：合理的数据库索引设计提高查询速度
- **分文件处理**：按文件分组处理，减少内存占用

### 质量控制

- **ppm误差验证**：确保匹配质量在可接受范围内
- **综合得分评估**：多维度评估匹配质量
- **统计信息反馈**：提供详细的匹配统计信息

### 用户友好

- **进度显示**：实时显示匹配进度
- **结果可视化**：表格形式展示匹配结果
- **数据导出**：支持CSV格式导出
- **操作可逆**：支持重置匹配结果

## 测试验证

运行测试脚本验证功能：

```r
source("test_ms_matching.R")
test_result <- test_ms_matching()
```

测试内容包括：
- 数据库版本检查和升级
- 数据完整性验证
- 匹配算法执行
- 结果质量评估

## 注意事项

1. **数据库备份**：升级前会自动创建备份，建议手动备份重要数据
2. **内存使用**：大数据集可能需要较多内存，建议调整batch_size参数
3. **匹配质量**：根据仪器精度调整ppm容差，过小可能导致匹配率低，过大可能导致错误匹配
4. **数据依赖**：需要同时存在MS1和MS2数据才能进行匹配

## 故障排除

### 常见问题

1. **匹配率过低**
   - 检查ppm容差设置是否合适
   - 验证MS1和MS2数据的完整性
   - 检查前体离子扫描编号是否正确

2. **匹配速度慢**
   - 减小batch_size参数
   - 检查数据库索引是否正确创建
   - 确保有足够的系统内存

3. **数据库升级失败**
   - 检查数据库文件权限
   - 确保数据库文件未被其他程序占用
   - 查看错误日志获取详细信息

### 日志查看

匹配过程中的详细日志会输出到控制台，包括：
- 处理进度信息
- 匹配统计数据
- 错误和警告信息

## 技术支持

如遇到问题，请提供以下信息：
- 数据库文件大小和记录数量
- 匹配参数设置
- 错误信息和日志
- 系统环境信息
