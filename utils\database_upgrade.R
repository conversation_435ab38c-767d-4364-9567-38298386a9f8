# 数据库升级脚本
# 用于升级现有数据库以支持MS2-MS1峰匹配功能

# 加载必要的包
load_upgrade_packages <- function() {
  required_packages <- c("DBI", "RSQLite")
  
  for (pkg in required_packages) {
    if (!requireNamespace(pkg, quietly = TRUE)) {
      stop(paste("Required package", pkg, "is not installed. Please install it first."))
    }
  }
  
  library(DBI)
  library(RSQLite)
}

# 检查数据库版本
get_database_version <- function(db_path) {
  if (!file.exists(db_path)) {
    return(NULL)
  }
  
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    # 检查是否存在版本表
    if (DBI::dbExistsTable(con, "database_version")) {
      version_info <- DBI::dbGetQuery(con, "SELECT version FROM database_version ORDER BY updated_at DESC LIMIT 1")
      if (nrow(version_info) > 0) {
        return(version_info$version[1])
      }
    }
    
    # 如果没有版本表，检查是否存在ms1_peak_id字段来判断版本
    table_info <- DBI::dbGetQuery(con, "PRAGMA table_info(ms2_spectra_data)")
    if ("ms1_peak_id" %in% table_info$name) {
      return("1.1")  # 支持MS2-MS1匹配的版本
    } else {
      return("1.0")  # 基础版本
    }
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
}

# 创建版本表
create_version_table <- function(con) {
  version_table_sql <- "
    CREATE TABLE IF NOT EXISTS database_version (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      version TEXT NOT NULL,
      description TEXT,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  "
  
  DBI::dbExecute(con, version_table_sql)
}

# 记录数据库版本
record_database_version <- function(con, version, description = NULL) {
  create_version_table(con)
  
  insert_sql <- "INSERT INTO database_version (version, description) VALUES (?, ?)"
  DBI::dbExecute(con, insert_sql, list(version, description))
}

# 升级到版本1.1（添加MS2-MS1匹配支持）
upgrade_to_v1_1 <- function(db_path) {
  cat("=== 升级数据库到版本1.1 ===\n")
  cat("添加MS2-MS1峰匹配支持\n")
  
  if (!file.exists(db_path)) {
    stop("数据库文件不存在:", db_path)
  }
  
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    # 开始事务
    DBI::dbBegin(con)
    
    # 检查ms1_peak_id字段是否已存在
    table_info <- DBI::dbGetQuery(con, "PRAGMA table_info(ms2_spectra_data)")
    
    if (!"ms1_peak_id" %in% table_info$name) {
      cat("添加ms1_peak_id字段到ms2_spectra_data表...\n")
      
      # 添加字段
      DBI::dbExecute(con, "ALTER TABLE ms2_spectra_data ADD COLUMN ms1_peak_id INTEGER")
      
      # 添加索引
      DBI::dbExecute(con, "CREATE INDEX IF NOT EXISTS idx_ms2_spectra_ms1_peak_id ON ms2_spectra_data(ms1_peak_id)")
      DBI::dbExecute(con, "CREATE INDEX IF NOT EXISTS idx_ms2_spectra_file_prec_scan ON ms2_spectra_data(file_id, prec_scan_num)")
      
      cat("ms1_peak_id字段添加完成\n")
    } else {
      cat("ms1_peak_id字段已存在，跳过添加\n")
    }
    
    # 记录版本信息
    record_database_version(con, "1.1", "添加MS2-MS1峰匹配支持")
    
    # 提交事务
    DBI::dbCommit(con)
    
    cat("数据库升级到版本1.1完成\n")
    return(TRUE)
    
  }, error = function(e) {
    DBI::dbRollback(con)
    cat("数据库升级失败:", e$message, "\n")
    return(FALSE)
  }, finally = {
    DBI::dbDisconnect(con)
  })
}

# 主升级函数
upgrade_database <- function(db_path, target_version = "latest") {
  cat("=== 数据库升级检查 ===\n")
  cat("数据库路径:", db_path, "\n")
  
  # 加载必要的包
  load_upgrade_packages()
  
  if (!file.exists(db_path)) {
    stop("数据库文件不存在:", db_path)
  }
  
  # 获取当前版本
  current_version <- get_database_version(db_path)
  cat("当前数据库版本:", current_version %||% "未知", "\n")
  
  # 确定目标版本
  if (target_version == "latest") {
    target_version <- "1.1"  # 当前最新版本
  }
  
  cat("目标版本:", target_version, "\n")
  
  # 检查是否需要升级
  if (!is.null(current_version) && current_version == target_version) {
    cat("数据库已是最新版本，无需升级\n")
    return(TRUE)
  }
  
  # 执行升级
  success <- TRUE
  
  if (is.null(current_version) || current_version == "1.0") {
    if (target_version >= "1.1") {
      success <- upgrade_to_v1_1(db_path)
      if (!success) {
        return(FALSE)
      }
    }
  }
  
  if (success) {
    cat("数据库升级完成\n")
  } else {
    cat("数据库升级失败\n")
  }
  
  return(success)
}

# 验证升级结果
validate_upgrade <- function(db_path, expected_version) {
  current_version <- get_database_version(db_path)
  
  if (current_version == expected_version) {
    cat("升级验证通过，当前版本:", current_version, "\n")
    return(TRUE)
  } else {
    cat("升级验证失败，期望版本:", expected_version, "，实际版本:", current_version, "\n")
    return(FALSE)
  }
}

# 备份数据库
backup_database <- function(db_path) {
  if (!file.exists(db_path)) {
    return(FALSE)
  }
  
  backup_path <- paste0(db_path, ".backup.", format(Sys.time(), "%Y%m%d_%H%M%S"))
  
  tryCatch({
    file.copy(db_path, backup_path)
    cat("数据库备份完成:", backup_path, "\n")
    return(backup_path)
  }, error = function(e) {
    cat("数据库备份失败:", e$message, "\n")
    return(FALSE)
  })
}

# 安全升级（包含备份）
safe_upgrade_database <- function(db_path, target_version = "latest") {
  cat("=== 安全数据库升级 ===\n")
  
  # 创建备份
  backup_path <- backup_database(db_path)
  if (backup_path == FALSE) {
    cat("备份失败，升级中止\n")
    return(FALSE)
  }
  
  # 执行升级
  success <- upgrade_database(db_path, target_version)
  
  if (success) {
    cat("升级成功，备份文件:", backup_path, "\n")
    cat("如果升级后出现问题，可以使用备份文件恢复\n")
  } else {
    cat("升级失败，正在恢复备份...\n")
    if (file.copy(backup_path, db_path, overwrite = TRUE)) {
      cat("数据库已恢复到升级前状态\n")
    } else {
      cat("恢复失败，请手动恢复备份文件:", backup_path, "\n")
    }
  }
  
  return(success)
}
