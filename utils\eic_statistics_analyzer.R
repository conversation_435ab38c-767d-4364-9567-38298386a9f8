# EIC统计分析器
# 用于分析同位素内标EIC数据的统计指标，包括峰型、半峰宽、峰面积、信噪比等

# 加载必要的包和脚本
safe_source("utils/logger.R")

# 加载必要的包
load_eic_statistics_packages <- function() {
  required_packages <- c("dplyr", "stats", "pracma", "signal")
  
  for (pkg in required_packages) {
    if (!requireNamespace(pkg, quietly = TRUE)) {
      # 对于可选包，给出警告而不是错误
      if (pkg %in% c("pracma", "signal")) {
        log_warning(paste("Optional package", pkg, "is not installed. Some advanced features may not be available."))
      } else {
        stop(paste("Required package", pkg, "is not installed. Please install it first."))
      }
    } else {
      suppressPackageStartupMessages(library(pkg, character.only = TRUE))
    }
  }
}

# EIC统计分析器类
EICStatisticsAnalyzer <- setRefClass("EICStatisticsAnalyzer",
  fields = list(
    noise_threshold_factor = "numeric",  # 噪音阈值因子
    min_peak_points = "numeric",         # 最小峰点数
    gaussian_fit_method = "character"    # 高斯拟合方法
  ),
  
  methods = list(
    initialize = function(noise_threshold_factor = 3, min_peak_points = 5, gaussian_fit_method = "nls") {
      .self$noise_threshold_factor <- noise_threshold_factor
      .self$min_peak_points <- min_peak_points
      .self$gaussian_fit_method <- gaussian_fit_method
      
      load_eic_statistics_packages()
      log_info("EIC统计分析器初始化完成")
    },
    
    # 计算基线噪音水平
    calculate_baseline_noise = function(intensities) {
      if (length(intensities) < 10) {
        return(list(noise_level = 0, baseline = 0))
      }
      
      # 使用中位数绝对偏差(MAD)估计噪音水平
      sorted_intensities <- sort(intensities)
      n <- length(sorted_intensities)
      
      # 取前30%的数据点作为基线区域
      baseline_points <- sorted_intensities[1:max(3, floor(n * 0.3))]
      baseline <- median(baseline_points)
      
      # 计算噪音水平
      noise_level <- mad(baseline_points, constant = 1.4826)
      
      return(list(
        noise_level = noise_level,
        baseline = baseline,
        noise_threshold = baseline + noise_threshold_factor * noise_level
      ))
    },
    
    # 过滤噪音数据点
    filter_noise_points = function(rtime, intensities) {
      if (length(rtime) != length(intensities)) {
        stop("rtime和intensities长度不匹配")
      }
      
      noise_info <- calculate_baseline_noise(intensities)
      
      # 过滤低于噪音阈值的点
      valid_indices <- intensities >= noise_info$noise_threshold
      
      if (sum(valid_indices) < min_peak_points) {
        log_warning("过滤噪音后数据点不足，使用原始数据")
        return(list(
          rtime = rtime,
          intensities = intensities,
          noise_info = noise_info,
          filtered = FALSE
        ))
      }
      
      return(list(
        rtime = rtime[valid_indices],
        intensities = intensities[valid_indices],
        noise_info = noise_info,
        filtered = TRUE
      ))
    },
    
    # 高斯分布拟合
    fit_gaussian_peak = function(rtime, intensities) {
      tryCatch({
        if (length(rtime) < min_peak_points) {
          return(NULL)
        }
        
        # 初始参数估计
        max_intensity <- max(intensities)
        max_index <- which.max(intensities)
        center_rt <- rtime[max_index]
        
        # 估计半峰宽
        half_max <- max_intensity / 2
        left_indices <- which(rtime < center_rt & intensities >= half_max)
        right_indices <- which(rtime > center_rt & intensities >= half_max)
        
        if (length(left_indices) > 0 && length(right_indices) > 0) {
          fwhm_estimate <- max(rtime[right_indices]) - min(rtime[left_indices])
        } else {
          fwhm_estimate <- (max(rtime) - min(rtime)) / 4
        }
        
        sigma_estimate <- fwhm_estimate / (2 * sqrt(2 * log(2)))
        
        # 高斯函数
        gaussian_func <- function(x, a, mu, sigma) {
          a * exp(-((x - mu)^2) / (2 * sigma^2))
        }
        
        # 非线性最小二乘拟合
        if (gaussian_fit_method == "nls") {
          fit <- nls(
            intensities ~ gaussian_func(rtime, a, mu, sigma),
            start = list(a = max_intensity, mu = center_rt, sigma = sigma_estimate),
            control = nls.control(maxiter = 100, warnOnly = TRUE)
          )
          
          params <- coef(fit)
          r_squared <- 1 - sum(residuals(fit)^2) / sum((intensities - mean(intensities))^2)
          
          return(list(
            amplitude = params[["a"]],
            center = params[["mu"]],
            sigma = params[["sigma"]],
            fwhm = 2 * sqrt(2 * log(2)) * params[["sigma"]],
            r_squared = r_squared,
            fit_success = TRUE,
            method = "nls"
          ))
        }
        
      }, error = function(e) {
        log_warning(paste("高斯拟合失败:", e$message))
        return(NULL)
      })
    },
    
    # 计算峰面积
    calculate_peak_area = function(rtime, intensities, method = "trapezoid") {
      if (length(rtime) < 2) {
        return(0)
      }
      
      if (method == "trapezoid") {
        # 梯形积分法
        area <- 0
        for (i in 1:(length(rtime) - 1)) {
          dt <- rtime[i + 1] - rtime[i]
          area <- area + dt * (intensities[i] + intensities[i + 1]) / 2
        }
        return(area)
      } else if (method == "simpson" && requireNamespace("pracma", quietly = TRUE)) {
        # 辛普森积分法（需要pracma包）
        return(pracma::trapz(rtime, intensities))
      } else {
        # 简单矩形法
        dt <- mean(diff(rtime))
        return(sum(intensities) * dt)
      }
    },
    
    # 计算信噪比
    calculate_signal_to_noise = function(intensities, peak_intensity = NULL) {
      if (is.null(peak_intensity)) {
        peak_intensity <- max(intensities)
      }
      
      noise_info <- calculate_baseline_noise(intensities)
      
      if (noise_info$noise_level == 0) {
        return(Inf)
      }
      
      return(peak_intensity / noise_info$noise_level)
    },
    
    # 评估峰型质量
    evaluate_peak_shape = function(gaussian_fit, rtime, intensities) {
      if (is.null(gaussian_fit) || !gaussian_fit$fit_success) {
        return(list(
          shape_quality = "差",
          symmetry_score = 0,
          tailing_factor = NA,
          shape_score = 0
        ))
      }
      
      # 基于R²评估拟合质量
      r_squared <- gaussian_fit$r_squared
      
      # 计算对称性评分
      center_rt <- gaussian_fit$center
      center_index <- which.min(abs(rtime - center_rt))
      
      if (center_index > 1 && center_index < length(rtime)) {
        left_intensities <- intensities[1:center_index]
        right_intensities <- rev(intensities[center_index:length(intensities)])
        
        # 计算左右对称性
        min_length <- min(length(left_intensities), length(right_intensities))
        if (min_length > 2) {
          left_part <- left_intensities[(length(left_intensities) - min_length + 1):length(left_intensities)]
          right_part <- right_intensities[1:min_length]
          
          symmetry_score <- cor(left_part, right_part, use = "complete.obs")
          if (is.na(symmetry_score)) symmetry_score <- 0
        } else {
          symmetry_score <- 0
        }
      } else {
        symmetry_score <- 0
      }
      
      # 综合评分
      shape_score <- (r_squared * 0.7 + max(0, symmetry_score) * 0.3)
      
      # 峰型质量分级
      if (shape_score >= 0.8) {
        shape_quality <- "优"
      } else if (shape_score >= 0.6) {
        shape_quality <- "良"
      } else if (shape_score >= 0.4) {
        shape_quality <- "中"
      } else {
        shape_quality <- "差"
      }
      
      return(list(
        shape_quality = shape_quality,
        symmetry_score = symmetry_score,
        r_squared = r_squared,
        shape_score = shape_score
      ))
    },
    
    # 分析单个样本的EIC数据
    analyze_sample_eic = function(eic_points, target_mz) {
      if (is.null(eic_points) || length(eic_points) == 0) {
        return(NULL)
      }
      
      # 提取数据
      rtime <- sapply(eic_points, function(x) x$rtime)
      intensities <- sapply(eic_points, function(x) x$intensity)
      mz_values <- sapply(eic_points, function(x) x$mz)
      
      if (length(rtime) == 0) {
        return(NULL)
      }
      
      # 找到最高强度点
      max_index <- which.max(intensities)
      apex_rtime <- rtime[max_index]
      apex_intensity <- intensities[max_index]
      apex_mz <- mz_values[max_index]
      
      # 计算mz偏差
      mz_error_ppm <- abs(apex_mz - target_mz) / target_mz * 1e6
      
      # 过滤噪音点
      filtered_data <- filter_noise_points(rtime, intensities)
      
      # 计算mz范围（使用非噪音点）
      if (filtered_data$filtered) {
        valid_indices <- intensities >= filtered_data$noise_info$noise_threshold
        mz_range <- range(mz_values[valid_indices])
      } else {
        mz_range <- range(mz_values)
      }
      
      # 高斯拟合
      gaussian_fit <- fit_gaussian_peak(filtered_data$rtime, filtered_data$intensities)
      
      # 峰型评估
      peak_shape <- evaluate_peak_shape(gaussian_fit, filtered_data$rtime, filtered_data$intensities)
      
      # 计算峰面积
      peak_area <- calculate_peak_area(filtered_data$rtime, filtered_data$intensities)
      
      # 计算信噪比
      snr <- calculate_signal_to_noise(intensities, apex_intensity)
      
      return(list(
        apex_rtime = apex_rtime,
        apex_intensity = apex_intensity,
        apex_mz = apex_mz,
        mz_error_ppm = mz_error_ppm,
        mz_range = mz_range,
        peak_shape_quality = peak_shape$shape_quality,
        peak_shape_score = peak_shape$shape_score,
        fwhm = if (!is.null(gaussian_fit)) gaussian_fit$fwhm else NA,
        peak_area = peak_area,
        signal_to_noise = snr,
        total_points = length(eic_points),
        valid_points = sum(intensities >= filtered_data$noise_info$noise_threshold),
        gaussian_fit = gaussian_fit,
        noise_info = filtered_data$noise_info
      ))
    }
  )
)

    # 批量分析多个样本的EIC数据
    analyze_multiple_samples_eic = function(eic_data, compound_name) {
      if (is.null(eic_data) || is.null(eic_data$eic_data_by_file)) {
        return(NULL)
      }

      target_mz <- eic_data$isotope_info$target_mz
      results <- list()

      for (file_id in names(eic_data$eic_data_by_file)) {
        file_data <- eic_data$eic_data_by_file[[file_id]]

        if (!is.null(file_data$eic_points) && length(file_data$eic_points) > 0) {
          sample_analysis <- analyze_sample_eic(file_data$eic_points, target_mz)

          if (!is.null(sample_analysis)) {
            results[[file_id]] <- list(
              file_info = file_data$file_info,
              analysis = sample_analysis
            )
          }
        }
      }

      return(results)
    },

    # 生成统计分析报告
    generate_statistics_report = function(analysis_results, compound_name) {
      if (is.null(analysis_results) || length(analysis_results) == 0) {
        return(data.frame())
      }

      report_data <- data.frame(
        化合物名称 = character(0),
        样本文件 = character(0),
        样本类型 = character(0),
        保留时间 = numeric(0),
        最大强度 = numeric(0),
        实际mz = numeric(0),
        mz偏差_ppm = numeric(0),
        mz范围_min = numeric(0),
        mz范围_max = numeric(0),
        峰型质量 = character(0),
        峰型评分 = numeric(0),
        半峰宽 = numeric(0),
        峰面积 = numeric(0),
        信噪比 = numeric(0),
        数据点数 = integer(0),
        有效点数 = integer(0),
        stringsAsFactors = FALSE
      )

      for (file_id in names(analysis_results)) {
        result <- analysis_results[[file_id]]
        file_info <- result$file_info
        analysis <- result$analysis

        new_row <- data.frame(
          化合物名称 = compound_name,
          样本文件 = file_info$file_name %||% file_id,
          样本类型 = file_info$sample_type %||% "Unknown",
          保留时间 = round(analysis$apex_rtime, 3),
          最大强度 = round(analysis$apex_intensity, 0),
          实际mz = round(analysis$apex_mz, 4),
          mz偏差_ppm = round(analysis$mz_error_ppm, 2),
          mz范围_min = round(analysis$mz_range[1], 4),
          mz范围_max = round(analysis$mz_range[2], 4),
          峰型质量 = analysis$peak_shape_quality,
          峰型评分 = round(analysis$peak_shape_score, 3),
          半峰宽 = if (!is.na(analysis$fwhm)) round(analysis$fwhm, 3) else NA,
          峰面积 = round(analysis$peak_area, 0),
          信噪比 = round(analysis$signal_to_noise, 1),
          数据点数 = analysis$total_points,
          有效点数 = analysis$valid_points,
          stringsAsFactors = FALSE
        )

        report_data <- rbind(report_data, new_row)
      }

      return(report_data)
    },

    # 识别异常数据
    identify_outliers = function(report_data) {
      if (nrow(report_data) == 0) {
        return(report_data)
      }

      # 添加异常标记列
      report_data$异常标记 <- ""

      # 检查保留时间异常（基于四分位距）
      if (nrow(report_data) > 3) {
        rt_q1 <- quantile(report_data$保留时间, 0.25, na.rm = TRUE)
        rt_q3 <- quantile(report_data$保留时间, 0.75, na.rm = TRUE)
        rt_iqr <- rt_q3 - rt_q1
        rt_outliers <- report_data$保留时间 < (rt_q1 - 1.5 * rt_iqr) |
                      report_data$保留时间 > (rt_q3 + 1.5 * rt_iqr)

        report_data$异常标记[rt_outliers] <- paste0(report_data$异常标记[rt_outliers], "RT异常;")
      }

      # 检查mz偏差异常（>20 ppm）
      mz_outliers <- report_data$mz偏差_ppm > 20
      report_data$异常标记[mz_outliers] <- paste0(report_data$异常标记[mz_outliers], "mz偏差大;")

      # 检查峰型质量异常
      shape_outliers <- report_data$峰型质量 == "差"
      report_data$异常标记[shape_outliers] <- paste0(report_data$异常标记[shape_outliers], "峰型差;")

      # 检查信噪比异常（<10）
      snr_outliers <- report_data$信噪比 < 10
      report_data$异常标记[snr_outliers] <- paste0(report_data$异常标记[snr_outliers], "信噪比低;")

      # 清理异常标记
      report_data$异常标记 <- gsub(";$", "", report_data$异常标记)
      report_data$异常标记[report_data$异常标记 == ""] <- "正常"

      return(report_data)
    }
  )
)

# 创建EIC统计分析器实例
create_eic_statistics_analyzer <- function(noise_threshold_factor = 3, min_peak_points = 5, gaussian_fit_method = "nls") {
  return(EICStatisticsAnalyzer$new(noise_threshold_factor, min_peak_points, gaussian_fit_method))
}

# 从JSON数据批量分析EIC统计信息
analyze_eic_statistics_from_json <- function(json_data, output_file = NULL) {
  tryCatch({
    # 创建分析器
    analyzer <- create_eic_statistics_analyzer()

    # 解析JSON数据
    if (is.character(json_data)) {
      if (file.exists(json_data)) {
        # 从文件读取
        eic_data <- jsonlite::fromJSON(json_data, simplifyVector = FALSE)
      } else {
        # 直接解析JSON字符串
        eic_data <- jsonlite::fromJSON(json_data, simplifyVector = FALSE)
      }
    } else {
      # 直接使用列表数据
      eic_data <- json_data
    }

    if (is.null(eic_data$compounds)) {
      stop("JSON数据中未找到compounds节点")
    }

    # 分析每个化合物
    all_reports <- list()

    for (compound_name in names(eic_data$compounds)) {
      compound_data <- eic_data$compounds[[compound_name]]

      # 分析该化合物的EIC数据
      analysis_results <- analyzer$analyze_multiple_samples_eic(compound_data, compound_name)

      if (!is.null(analysis_results) && length(analysis_results) > 0) {
        # 生成统计报告
        report <- analyzer$generate_statistics_report(analysis_results, compound_name)

        # 识别异常数据
        report <- analyzer$identify_outliers(report)

        all_reports[[compound_name]] <- report
      }
    }

    # 合并所有报告
    if (length(all_reports) > 0) {
      final_report <- do.call(rbind, all_reports)
      rownames(final_report) <- NULL

      # 保存到文件
      if (!is.null(output_file)) {
        write.csv(final_report, output_file, row.names = FALSE, fileEncoding = "UTF-8")
        log_info(paste("EIC统计分析报告已保存到:", output_file))
      }

      return(final_report)
    } else {
      log_warning("未生成任何统计分析报告")
      return(data.frame())
    }

  }, error = function(e) {
    log_error(paste("EIC统计分析失败:", e$message))
    return(NULL)
  })
}
