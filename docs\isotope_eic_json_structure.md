# 同位素内标离子EIC数据JSON结构设计

## 概述

本文档定义了用于前端交互式展示的同位素内标离子EIC数据JSON文件结构。该结构包含EIC数据和对应的MS2碎片信息，支持多样本、多化合物的数据组织。

## JSON结构设计

### 顶层结构

```json
{
  "metadata": {
    "extraction_time": "2025-08-01 10:30:00",
    "total_compounds": 5,
    "extraction_parameters": {
      "tolerance_ppm": 20,
      "min_intensity": 1000
    },
    "database_path": "/path/to/spectra.db"
  },
  "compounds": {
    "化合物名称1": { /* 化合物EIC数据 */ },
    "化合物名称2": { /* 化合物EIC数据 */ }
  }
}
```

### 化合物EIC数据结构

```json
{
  "isotope_info": {
    "compound_name": "咖啡因-13C3",
    "target_mz": 198.0842,
    "ion_type": "[M+H]+",
    "ionization_mode": "positive",
    "retention_time": 4.2,
    "molecular_weight": 197.0769
  },
  "eic_data_by_file": {
    "file_id_1": {
      "file_info": {
        "file_id": "file_20250801_103000_001",
        "file_name": "QC_001_spectra.rds",
        "sample_type": "QC",
        "scan_mode": "positive"
      },
      "eic_points": [
        {
          "peak_id": 12345,
          "spectrum_id": 678,
          "rtime": 4.18,
          "mz": 198.0845,
          "intensity": 125000,
          "mz_error_ppm": 1.5,
          "scan_index": 234
        }
      ]
    }
  },
  "ms2_data_by_peak": {
    "12345": [
      {
        "spectrum_id": 1234,
        "rtime": 4.19,
        "precursor_mz": 198.0845,
        "precursor_intensity": 125000,
        "precursor_charge": 1,
        "collision_energy": 25.0,
        "isolation_window_lower_mz": 197.5,
        "isolation_window_target_mz": 198.0,
        "isolation_window_upper_mz": 198.5,
        "fragments": [
          {
            "mz": 140.0348,
            "intensity": 85000
          },
          {
            "mz": 110.0712,
            "intensity": 45000
          }
        ]
      }
    ]
  },
  "summary": {
    "total_files": 12,
    "total_eic_points": 48,
    "total_ms2_spectra": 15,
    "total_ms2_fragments": 156
  },
  "extraction_info": {
    "extraction_time": "2025-08-01 10:30:00",
    "extraction_parameters": {
      "tolerance_ppm": 20,
      "min_intensity": 1000
    }
  }
}
```

## 数据字段说明

### metadata（元数据）
- `extraction_time`: 数据提取时间
- `total_compounds`: 总化合物数量
- `extraction_parameters`: 提取参数
  - `tolerance_ppm`: 质量容差（ppm）
  - `min_intensity`: 最小强度阈值
- `database_path`: 数据库文件路径

### isotope_info（同位素内标信息）
- `compound_name`: 化合物名称
- `target_mz`: 目标质荷比
- `ion_type`: 离子类型（如[M+H]+, [M-H]-）
- `ionization_mode`: 离子化模式（positive/negative）
- `retention_time`: 理论保留时间
- `molecular_weight`: 分子质量

### eic_data_by_file（按文件组织的EIC数据）
- 键为`file_id`，值为该文件中的EIC数据
- `file_info`: 文件基本信息
  - `file_id`: 文件ID
  - `file_name`: 文件名
  - `sample_type`: 样本类型（QC, STD, BLANK, SAMPLE）
  - `scan_mode`: 扫描模式（positive/negative）
- `eic_points`: EIC数据点数组
  - `peak_id`: 峰ID（用于关联MS2数据）
  - `spectrum_id`: 谱图ID
  - `rtime`: 保留时间
  - `mz`: 实际质荷比
  - `intensity`: 强度
  - `mz_error_ppm`: 质量偏差（ppm）
  - `scan_index`: 扫描索引

### ms2_data_by_peak（按峰组织的MS2数据）
- 键为`peak_id`（字符串），值为该峰对应的MS2谱图数组
- 每个MS2谱图包含：
  - `spectrum_id`: MS2谱图ID
  - `rtime`: MS2扫描时间
  - `precursor_mz`: 前体离子质荷比
  - `precursor_intensity`: 前体离子强度
  - `precursor_charge`: 前体离子电荷
  - `collision_energy`: 碰撞能
  - `isolation_window_*`: 隔离窗口参数
  - `fragments`: 碎片离子数组
    - `mz`: 碎片离子质荷比
    - `intensity`: 碎片离子强度

### summary（统计摘要）
- `total_files`: 涉及的文件总数
- `total_eic_points`: EIC数据点总数
- `total_ms2_spectra`: MS2谱图总数
- `total_ms2_fragments`: MS2碎片离子总数

## 前端使用建议

### EIC图表展示
1. 使用`eic_data_by_file`中的数据绘制EIC曲线
2. X轴：`rtime`，Y轴：`intensity`
3. 不同文件用不同颜色或线型区分
4. 点击EIC点可显示对应的MS2信息

### MS2碎片图表展示
1. 使用`ms2_data_by_peak`中的数据绘制MS2谱图
2. X轴：`mz`，Y轴：`intensity`
3. 显示前体离子信息和碰撞能
4. 支持多个MS2谱图的切换显示

### 数据过滤和筛选
1. 按样本类型过滤（QC, STD, BLANK, SAMPLE）
2. 按质量偏差范围过滤
3. 按强度阈值过滤
4. 按保留时间窗口过滤

## 性能优化建议

1. **分页加载**: 对于大量数据，考虑分页或按需加载
2. **数据压缩**: 使用gzip压缩JSON文件
3. **索引优化**: 为频繁查询的字段建立索引
4. **缓存策略**: 缓存常用的查询结果
