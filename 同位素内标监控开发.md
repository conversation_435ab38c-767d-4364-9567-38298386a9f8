# 同位素内标监控
## 监控指标
EIC，峰型，半高峰宽，rt、mz、intensity、MS2碎片在QC、实验样本中的稳定性，灵敏度(S/N)。
## 同位素化合物信息管理(已完成)
| 化合物名称 | 分子质量 | 保留时间 | 离子类型 | 离子质荷比(m/z) |
| --- | --- | --- | --- | --- |

- 添加方式：手动添加，基于yaml文件导入
- 编辑、删除、刷新
- 导出信息到yaml文件
## 样品中同位素化合物数据提取
从项目 spectra.db 中提取所有离子的EIC、MS2碎片信息
基于同位素内标的yaml配置文件中的信息，去对应的样本原始数据中找到每个同位素化合物的 EIC、MS2碎片，然后计算峰型、半峰宽、RT、MZ、峰面积等信息
## 同位素内标监控结果可视化


功能有点复杂，让我们分布开发实现吧：
首先让我们开发在数据库中添加MS2与MS1的匹配功能：
当前数据库中需要在ms2_spectra_data表中添加ms1_peak_id字段作为外键，指向ms1_peaks_data表中的peak_id字段，表示该MS2扫描对应的MS1峰数据。
匹配逻辑如下：
首先使用file_id字段和prec_scan_num字段在ms1_spectra_data表中查找对应的MS1扫描数据(file_id相等，且ms1_spectra_data表中scan_index与prec_scan_num也相等)，然后基于ms1_spectra_data表的spectrum_id字段在ms1_peaks_data表中查找对应的MS1峰数据，并挑选ms1_peaks_data表mz值与precursor_mz偏差在20ppm以内，且intensity较高并于precursor_intensity相近的一个峰，赋值给ms2_spectra_data表中添加ms1_peak_id。
基于匹配逻辑设计高性能算法，并编写代码实现。

接下来有以下功能需要开发：
【第一部分】
然后让我们开发同位素内标离子EIC数据提取功能：
同位素内标离子的EIC是展示同位素内标化合物每次MS1扫描在rtime轴上intensity的变化关系，通过MS1的mz和同位素内标的离子质荷比来识别同位素内标离子，注意扫描模式为negative的文件中只包含离子类型为[M-H]-的离子，positive的文件中只包含离子类型为[M+H]+的离子。具体步骤逻辑如下：
首先根据扫描模式和离子类型匹配在data_files中匹配要处理的文件file_id，根据同位素内标的离子质荷比在ms1_peaks_data表中的mz字段进行匹配，并返回匹配结果，根据返回匹配结果的spectrum_id字段，在ms1_spectra_data表中查找对应的rtime信息，根据返回的ms1_peaks_data匹配结果的peak_id字段，在ms2_spectra_data表中基于ms1_peak_id列查找对应的MS2信息，并基于MS2信息中的spectrum_id字段，在ms2_peaks_data表中查找对应的MS2碎片信息
为方便后续在前端中交互式展示同位素内标离子的EIC数据和对应的MS2碎片信息，需要基于以上步骤逻辑设计个json文件，存储同位素内标离子信息提取结果
【第二部分】
EIC数据在各样本中的统计分析，并总结为信息表格(高亮显示异常数据)，内容如下：
该同位素内标在各样本中的rtime(使用intensity最高的扫描点作为rtime)、mz偏差(使用intensity最高的扫描点计算mz偏差)、扫描点mz范围(使用非噪音扫描点计算mz范围)、峰型(使用非噪音扫描点拟合高斯分布并计算评估峰型)、半峰宽(使用非噪音扫描点拟合高斯分布并计算评估半峰宽)、峰面积(使用非噪音扫描点拟合高斯分布并计算评估峰面积)、信噪比等
对应的MS2信息可不做统计。
【第三部分】
EIC数据与MS2碎片信息的可视化：
使用3D散点图展示不同样本的EIC数据，x轴为rtime(从左到右为从小到大)，竖轴为intensity(从上到下为从大到小)，z轴为sampleName，颜色为实际mz和同位素内标离子质荷比偏差，形状为是否有MS2碎片扫描结果
使用宽度为零的柱形图展示每个MS2碎片数据，x轴为mz，y轴为intensity，显示precursor的信息，但要防止图形太多导致的性能开销，想个方案解决一下。precursor信息详细注释：
"precScanNum" msLevel为2时，该次扫描对应的前体离子来源msLevel 1的扫描编号
"precursorMz" msLevel为2时，该次扫描对应的前体离子的mz
"precursorIntensity" msLevel为2时，该次扫描对应的前体离子的强度
"precursorCharge" msLevel为2时，该次扫描对应前体离子的电荷
"collisionEnergy" msLevel为2时，该次扫描对应的离子碎裂时使用的碰撞能
"isolationWindowLowerMz" msLevel为2时，该次扫描对应的前体离子的隔离窗口下限
"isolationWindowTargetMz" msLevel为2时，该次扫描对应的前体离子的隔离窗口中心
"isolationWindowUpperMz" msLevel为2时，该次扫描对应前体离子的隔离窗口上限

综合分析一下后让我们先实现第一部分，注意开发过程中若使用到依赖服务，优先考虑使用项目中已经定义的通用服务，通用服务的导入使用safe_source()

已经成功完成了第一部分同位素内标EIC数据提取功能的后端服务开发。以下是完成的主要工作：
✅ 已完成的任务
分析同位素内标离子EIC数据提取需求
深入分析了数据库结构和表关系
理解了MS1/MS2数据的关联逻辑
设计了完整的数据提取流程
创建同位素内标离子EIC数据提取器
实现了IsotopeEICExtractor RefClass类
包含完整的数据库查询方法
支持批量处理和错误处理
设计EIC数据存储的JSON结构
创建了详细的JSON结构设计文档
提供了完整的示例数据
设计了前端友好的数据格式
实现EIC数据提取的服务接口
创建了server层的响应式接口
集成到现有的服务架构中
支持实时状态显示和进度监控
测试EIC数据提取功能
编写了完整的测试框架
包含12个核心测试用例
创建了测试总结文档
🎯 核心功能特点
完整的数据流程: 从监控离子配置到JSON输出的端到端处理
高性能查询: 优化的SQL查询和参数化处理
用户友好: 响应式界面和实时状态反馈
扩展性强: 模块化设计，便于后续功能扩展
质量可靠: 完善的测试覆盖和错误处理
📁 创建的文件
核心实现文件:
utils/isotope_eic_extractor.R - EIC数据提取器核心实现
server/isotope_eic_server.R - 服务层接口

让我们继续实现第一部分的UI界面，并对第二部分、第三部分的功能进行开发，注意开发过程中若使用到依赖服务，优先考虑使用项目中已经定义的服务，服务的导入使用safe_source()