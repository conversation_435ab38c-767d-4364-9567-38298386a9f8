# 加载必要的脚本
source("utils/database_upgrade.R")
source("utils/ms_matching_algorithm.R")
db_path = "test/a1/results/spectra.db"
cat("=== MS2-MS1峰匹配功能测试 ===\n")
# 如果没有指定数据库路径，尝试查找
if (is.null(db_path)) {
# 查找可能的数据库路径
possible_paths <- c(
"data/spectra.db",
"test/QC3/data/spectra.db",
"projects/QC3/data/spectra.db"
)
for (path in possible_paths) {
if (file.exists(path)) {
db_path <- path
break
}
}
if (is.null(db_path)) {
cat("未找到数据库文件，请指定数据库路径\n")
return(FALSE)
}
}
cat("使用数据库:", db_path, "\n")
# 1. 检查数据库版本并升级
cat("\n1. 检查数据库版本...\n")
current_version <- get_database_version(db_path)
cat("当前版本:", current_version %||% "未知", "\n")
if (is.null(current_version) || current_version < "1.1") {
cat("需要升级数据库以支持MS2-MS1匹配功能\n")
upgrade_success <- safe_upgrade_database(db_path, "1.1")
if (!upgrade_success) {
cat("数据库升级失败\n")
return(FALSE)
}
}
# 2. 检查数据库内容
cat("\n2. 检查数据库内容...\n")
con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
tryCatch({
# 检查表结构
ms2_table_info <- DBI::dbGetQuery(con, "PRAGMA table_info(ms2_spectra_data)")
if (!"ms1_peak_id" %in% ms2_table_info$name) {
cat("错误：ms2_spectra_data表中缺少ms1_peak_id字段\n")
return(FALSE)
}
# 统计数据
ms1_count <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms1_spectra_data")$count
ms2_count <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms2_spectra_data")$count
ms1_peaks_count <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms1_peaks_data")$count
cat("MS1扫描数:", ms1_count, "\n")
cat("MS2扫描数:", ms2_count, "\n")
cat("MS1峰数:", ms1_peaks_count, "\n")
if (ms2_count == 0) {
cat("数据库中没有MS2数据，无法进行匹配测试\n")
return(FALSE)
}
if (ms1_peaks_count == 0) {
cat("数据库中没有MS1峰数据，无法进行匹配测试\n")
return(FALSE)
}
}, finally = {
DBI::dbDisconnect(con)
})
# 3. 执行匹配测试
cat("\n3. 执行MS2-MS1匹配...\n")
# 定义进度回调函数
progress_callback <- function(progress, matched_count, processed_count) {
cat("进度:", round(progress, 1), "%, 已匹配:", matched_count, ", 已处理:", processed_count, "\n")
}
# 执行匹配
result <- match_ms2_to_ms1_peaks(
db_path = db_path,
ppm_tolerance = 20,
batch_size = 500,
progress_callback = progress_callback
)
if (!result$success) {
cat("匹配失败:", result$error, "\n")
return(FALSE)
}
# 4. 验证匹配结果
cat("\n4. 验证匹配结果...\n")
stats <- get_matching_statistics(db_path)
cat("匹配统计:\n")
cat("  总MS2扫描数:", stats$total_ms2, "\n")
cat("  已匹配数:", stats$matched_ms2, "\n")
cat("  匹配率:", round(stats$match_rate * 100, 2), "%\n")
if (nrow(stats$file_statistics) > 0) {
cat("\n按文件统计:\n")
for (i in 1:nrow(stats$file_statistics)) {
file_stat <- stats$file_statistics[i, ]
cat("  ", basename(file_stat$file_name), ":",
file_stat$matched_ms2, "/", file_stat$total_ms2,
" (", round(file_stat$matched_ms2 / file_stat$total_ms2 * 100, 1), "%)\n")
}
}
# 5. 验证匹配质量
cat("\n5. 验证匹配质量...\n")
con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
tryCatch({
# 检查匹配的质量
quality_check_sql <- "
SELECT
COUNT(*) as total_matches,
AVG(ABS(m.precursor_mz - p.mz) / m.precursor_mz * 1e6) as avg_ppm_error,
MAX(ABS(m.precursor_mz - p.mz) / m.precursor_mz * 1e6) as max_ppm_error,
MIN(ABS(m.precursor_mz - p.mz) / m.precursor_mz * 1e6) as min_ppm_error
FROM ms2_spectra_data m
JOIN ms1_peaks_data p ON m.ms1_peak_id = p.peak_id
WHERE m.ms1_peak_id IS NOT NULL AND m.precursor_mz IS NOT NULL
"
quality_stats <- DBI::dbGetQuery(con, quality_check_sql)
if (nrow(quality_stats) > 0 && quality_stats$total_matches > 0) {
cat("匹配质量统计:\n")
cat("  匹配数量:", quality_stats$total_matches, "\n")
cat("  平均ppm误差:", round(quality_stats$avg_ppm_error, 2), "\n")
cat("  最大ppm误差:", round(quality_stats$max_ppm_error, 2), "\n")
cat("  最小ppm误差:", round(quality_stats$min_ppm_error, 2), "\n")
# 检查是否有异常的匹配
if (quality_stats$max_ppm_error > 25) {
cat("警告：发现ppm误差超过25的匹配，请检查匹配算法\n")
}
}
}, finally = {
DBI::dbDisconnect(con)
})
cat("\n=== 测试完成 ===\n")
cat("MS2-MS1峰匹配功能测试通过\n")
db_path = "test/a1/results/spectra1.db"
cat("=== MS2-MS1峰匹配功能测试 ===\n")
# 如果没有指定数据库路径，尝试查找
if (is.null(db_path)) {
# 查找可能的数据库路径
possible_paths <- c(
"data/spectra.db",
"test/QC3/data/spectra.db",
"projects/QC3/data/spectra.db"
)
for (path in possible_paths) {
if (file.exists(path)) {
db_path <- path
break
}
}
if (is.null(db_path)) {
cat("未找到数据库文件，请指定数据库路径\n")
return(FALSE)
}
}
cat("使用数据库:", db_path, "\n")
# 1. 检查数据库版本并升级
cat("\n1. 检查数据库版本...\n")
current_version <- get_database_version(db_path)
cat("当前版本:", current_version %||% "未知", "\n")
if (is.null(current_version) || current_version < "1.1") {
cat("需要升级数据库以支持MS2-MS1匹配功能\n")
upgrade_success <- safe_upgrade_database(db_path, "1.1")
if (!upgrade_success) {
cat("数据库升级失败\n")
return(FALSE)
}
}
# 2. 检查数据库内容
cat("\n2. 检查数据库内容...\n")
con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
tryCatch({
# 检查表结构
ms2_table_info <- DBI::dbGetQuery(con, "PRAGMA table_info(ms2_spectra_data)")
if (!"ms1_peak_id" %in% ms2_table_info$name) {
cat("错误：ms2_spectra_data表中缺少ms1_peak_id字段\n")
return(FALSE)
}
# 统计数据
ms1_count <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms1_spectra_data")$count
ms2_count <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms2_spectra_data")$count
ms1_peaks_count <- DBI::dbGetQuery(con, "SELECT COUNT(*) as count FROM ms1_peaks_data")$count
cat("MS1扫描数:", ms1_count, "\n")
cat("MS2扫描数:", ms2_count, "\n")
cat("MS1峰数:", ms1_peaks_count, "\n")
if (ms2_count == 0) {
cat("数据库中没有MS2数据，无法进行匹配测试\n")
return(FALSE)
}
if (ms1_peaks_count == 0) {
cat("数据库中没有MS1峰数据，无法进行匹配测试\n")
return(FALSE)
}
}, finally = {
DBI::dbDisconnect(con)
})
# 3. 执行匹配测试
cat("\n3. 执行MS2-MS1匹配...\n")
# 定义进度回调函数
progress_callback <- function(progress, matched_count, processed_count) {
cat("进度:", round(progress, 1), "%, 已匹配:", matched_count, ", 已处理:", processed_count, "\n")
}
# 执行匹配
result <- match_ms2_to_ms1_peaks(
db_path = db_path,
ppm_tolerance = 20,
batch_size = 500,
progress_callback = progress_callback
)
# 加载必要的包
library(DBI)
library(RSQLite)
library(data.table)
# 调试单个失败的MS2扫描
debug_single_ms2_failure <- function(db_path, spectrum_id) {
cat("=== 调试MS2扫描匹配失败 ===\n")
cat("数据库:", db_path, "\n")
cat("MS2 spectrum_id:", spectrum_id, "\n\n")
con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
tryCatch({
# 1. 获取MS2扫描信息
ms2_info <- DBI::dbGetQuery(con, "
SELECT m.*, d.file_name
FROM ms2_spectra_data m
JOIN data_files d ON m.file_id = d.file_id
WHERE m.spectrum_id = ?
", list(spectrum_id))
if (nrow(ms2_info) == 0) {
cat("错误: 找不到spectrum_id =", spectrum_id, "的MS2扫描\n")
return(FALSE)
}
ms2 <- ms2_info[1, ]
cat("1. MS2扫描信息:\n")
cat("   文件:", basename(ms2$file_name), "\n")
cat("   spectrum_id:", ms2$spectrum_id, "\n")
cat("   scan_index:", ms2$scan_index, "\n")
cat("   rtime:", ms2$rtime, "\n")
cat("   precursor_mz:", ms2$precursor_mz, "\n")
cat("   prec_scan_num:", ms2$prec_scan_num, "\n")
cat("   ms1_peak_id:", ms2$ms1_peak_id, "\n")
# 2. 检查基本条件
cat("\n2. 基本条件检查:\n")
if (is.na(ms2$prec_scan_num)) {
cat("   ❌ prec_scan_num为NULL - 这是失败的根本原因\n")
return(FALSE)
} else {
cat("   ✅ prec_scan_num有效:", ms2$prec_scan_num, "\n")
}
if (is.na(ms2$precursor_mz)) {
cat("   ❌ precursor_mz为NULL - 这是失败的根本原因\n")
return(FALSE)
} else {
cat("   ✅ precursor_mz有效:", ms2$precursor_mz, "\n")
}
# 3. 检查对应的MS1扫描
cat("\n3. 检查对应的MS1扫描:\n")
ms1_scan <- DBI::dbGetQuery(con, "
SELECT *
FROM ms1_spectra_data
WHERE file_id = ? AND scan_index = ?
", list(ms2$file_id, ms2$prec_scan_num))
if (nrow(ms1_scan) == 0) {
cat("   ❌ 找不到对应的MS1扫描 (file_id=", ms2$file_id, ", scan_index=", ms2$prec_scan_num, ")\n")
# 显示该文件的MS1扫描范围
ms1_range <- DBI::dbGetQuery(con, "
SELECT MIN(scan_index) as min_scan, MAX(scan_index) as max_scan, COUNT(*) as count
FROM ms1_spectra_data WHERE file_id = ?
", list(ms2$file_id))
cat("   该文件MS1扫描范围:", ms1_range$min_scan, "-", ms1_range$max_scan, "(共", ms1_range$count, "个)\n")
# 显示最接近的MS1扫描
nearby_ms1 <- DBI::dbGetQuery(con, "
SELECT scan_index, ABS(scan_index - ?) as distance
FROM ms1_spectra_data
WHERE file_id = ?
ORDER BY distance
LIMIT 5
", list(ms2$prec_scan_num, ms2$file_id))
if (nrow(nearby_ms1) > 0) {
cat("   最接近的MS1扫描:\n")
for (i in 1:nrow(nearby_ms1)) {
cat("     scan_index:", nearby_ms1$scan_index[i], "(距离:", nearby_ms1$distance[i], ")\n")
}
}
return(FALSE)
} else {
ms1 <- ms1_scan[1, ]
cat("   ✅ 找到对应的MS1扫描:\n")
cat("     spectrum_id:", ms1$spectrum_id, "\n")
cat("     scan_index:", ms1$scan_index, "\n")
cat("     rtime:", ms1$rtime, "\n")
}
# 4. 检查MS1峰数据
cat("\n4. 检查MS1峰数据:\n")
ms1_peaks <- DBI::dbGetQuery(con, "
SELECT *
FROM ms1_peaks_data
WHERE spectrum_id = ?
ORDER BY intensity DESC
", list(ms1$spectrum_id))
cat("   MS1峰数量:", nrow(ms1_peaks), "\n")
if (nrow(ms1_peaks) == 0) {
cat("   ❌ MS1扫描没有峰数据 - 这是失败的原因\n")
return(FALSE)
}
# 显示前5个最强的峰
cat("   前5个最强的峰:\n")
for (i in 1:min(5, nrow(ms1_peaks))) {
peak <- ms1_peaks[i, ]
ppm_error <- abs(peak$mz - ms2$precursor_mz) / ms2$precursor_mz * 1e6
cat("     峰", i, ": peak_id=", peak$peak_id, ", mz=", round(peak$mz, 4),
", intensity=", round(peak$intensity, 0), ", ppm_error=", round(ppm_error, 2), "\n")
}
# 5. 检查质量匹配
cat("\n5. 检查质量匹配 (ppm容差=20):\n")
ppm_tolerance <- 20
# 计算所有峰的ppm误差
ms1_peaks$ppm_error <- abs(ms1_peaks$mz - ms2$precursor_mz) / ms2$precursor_mz * 1e6
matching_peaks <- ms1_peaks[ms1_peaks$ppm_error <= ppm_tolerance, ]
cat("   质量匹配的峰数量:", nrow(matching_peaks), "\n")
if (nrow(matching_peaks) == 0) {
cat("   ❌ 没有质量匹配的峰 - 这是失败的原因\n")
# 显示最接近的峰
closest_peak <- ms1_peaks[which.min(ms1_peaks$ppm_error), ]
cat("   最接近的峰: peak_id=", closest_peak$peak_id,
", mz=", round(closest_peak$mz, 4),
", ppm_error=", round(closest_peak$ppm_error, 2), "\n")
return(FALSE)
} else {
cat("   ✅ 找到", nrow(matching_peaks), "个质量匹配的峰\n")
# 显示所有匹配的峰
for (i in 1:nrow(matching_peaks)) {
peak <- matching_peaks[i, ]
cat("     匹配峰", i, ": peak_id=", peak$peak_id,
", mz=", round(peak$mz, 4),
", intensity=", round(peak$intensity, 0),
", ppm_error=", round(peak$ppm_error, 2), "\n")
}
}
# 6. 模拟匹配算法
cat("\n6. 模拟匹配算法:\n")
if (nrow(matching_peaks) == 1) {
best_match <- matching_peaks[1, ]
cat("   只有1个匹配峰，直接选择: peak_id=", best_match$peak_id, "\n")
} else {
cat("   有", nrow(matching_peaks), "个匹配峰，需要选择最佳匹配\n")
# 计算综合得分
if (!is.na(ms2$precursor_intensity) && ms2$precursor_intensity > 0) {
cat("   使用前体强度信息计算得分\n")
matching_peaks$intensity_similarity <- 1 - abs(matching_peaks$intensity - ms2$precursor_intensity) /
(matching_peaks$intensity + ms2$precursor_intensity)
matching_peaks$combined_score <- (1 - matching_peaks$ppm_error / ppm_tolerance) * 0.6 +
matching_peaks$intensity_similarity * 0.4
} else {
cat("   没有前体强度信息，使用简化得分\n")
matching_peaks$combined_score <- (1 - matching_peaks$ppm_error / ppm_tolerance) * 0.7 +
(matching_peaks$intensity / max(matching_peaks$intensity)) * 0.3
}
# 显示得分
for (i in 1:nrow(matching_peaks)) {
peak <- matching_peaks[i, ]
cat("     峰", i, "得分:", round(peak$combined_score, 4),
" (peak_id=", peak$peak_id, ")\n")
}
best_match <- matching_peaks[which.max(matching_peaks$combined_score), ]
cat("   最佳匹配: peak_id=", best_match$peak_id,
", 得分=", round(best_match$combined_score, 4), "\n")
}
# 7. 结论
cat("\n7. 结论:\n")
if (!is.na(ms2$ms1_peak_id)) {
if (ms2$ms1_peak_id == best_match$peak_id) {
cat("   ✅ MS2已正确匹配到peak_id=", ms2$ms1_peak_id, "\n")
} else {
cat("   ⚠️  MS2匹配到了peak_id=", ms2$ms1_peak_id,
"，但算法建议是peak_id=", best_match$peak_id, "\n")
}
} else {
cat("   ❓ MS2未匹配，但根据分析应该匹配到peak_id=", best_match$peak_id, "\n")
cat("   这可能是算法bug，需要进一步调试！\n")
}
}, finally = {
DBI::dbDisconnect(con)
})
return(TRUE)
}
con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
tryCatch({
# 查找第一个未匹配的MS2扫描
first_failure <- DBI::dbGetQuery(con, "
SELECT spectrum_id, scan_index, precursor_mz, prec_scan_num
FROM ms2_spectra_data
WHERE ms1_peak_id IS NULL
ORDER BY spectrum_id
LIMIT 1
")
if (nrow(first_failure) > 0) {
cat("找到第一个失败的MS2扫描: spectrum_id =", first_failure$spectrum_id[1], "\n")
return(first_failure$spectrum_id[1])
} else {
cat("没有找到失败的MS2扫描\n")
return(NULL)
}
}, finally = {
DBI::dbDisconnect(con)
})
is.null(failed_spectrum_id)
# 查找第一个失败的MS2扫描
find_first_failure <- function(db_path) {
con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
tryCatch({
# 查找第一个未匹配的MS2扫描
first_failure <- DBI::dbGetQuery(con, "
SELECT spectrum_id, scan_index, precursor_mz, prec_scan_num
FROM ms2_spectra_data
WHERE ms1_peak_id IS NULL
ORDER BY spectrum_id
LIMIT 1
")
if (nrow(first_failure) > 0) {
cat("找到第一个失败的MS2扫描: spectrum_id =", first_failure$spectrum_id[1], "\n")
return(first_failure$spectrum_id[1])
} else {
cat("没有找到失败的MS2扫描\n")
return(NULL)
}
}, finally = {
DBI::dbDisconnect(con)
})
}
db_path <- "test/a1/results/spectra1.db"  # 根据实际路径调整
cat("查找第一个匹配失败的MS2扫描...\n")
failed_spectrum_id <- find_first_failure(db_path)
if (!is.null(failed_spectrum_id)) {
cat("\n开始详细调试...\n")
debug_single_ms2_failure(db_path, failed_spectrum_id)
}
