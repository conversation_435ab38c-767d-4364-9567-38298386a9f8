# 工作区页面服务器逻辑

# 检查是否在正确的环境中加载
# 在Shiny应用启动时，这些函数通过命名空间可用，即使不在全局环境中
if (exists("renderText", where = "package:shiny") || exists("renderText")) {
  # 只有在Shiny环境中才执行后续代码
  cat("[DEBUG] workspace_server.R: 在Shiny环境中，开始加载\n")

# 确保基本的日志函数存在
if (!exists("log_info")) {
  log_info <- function(msg) {
    cat("[INFO]", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), msg, "\n")
  }
}

if (!exists("log_warning")) {
  log_warning <- function(msg) {
    cat("[WARNING]", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), msg, "\n")
  }
}

if (!exists("log_error")) {
  log_error <- function(msg) {
    cat("[ERROR]", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), msg, "\n")
  }
}

# 在服务器启动时保存应用根路径
if (!exists("APP_ROOT_PATH", envir = .GlobalEnv)) {
  # 尝试从当前工作目录确定应用根路径
  current_wd <- getwd()
  if (file.exists(file.path(current_wd, "app.R"))) {
    # 当前目录就是应用根路径
    assign("APP_ROOT_PATH", current_wd, envir = .GlobalEnv)
    log_info(paste("保存应用根路径:", current_wd))
  } else {
    # 尝试向上查找
    search_dir <- current_wd
    for (i in 1:3) {
      parent_dir <- dirname(search_dir)
      if (file.exists(file.path(parent_dir, "app.R"))) {
        assign("APP_ROOT_PATH", parent_dir, envir = .GlobalEnv)
        log_info(paste("保存应用根路径:", parent_dir))
        break
      }
      search_dir <- parent_dir
    }
  }
}

# 加载必要的包
library(future)
library(promises)

# 加载必要的工具函数
tryCatch({
  safe_source("utils/project_manager.R", encoding = "UTF-8")
  safe_source("utils/path_manager.R", encoding = "UTF-8")
  safe_source("utils/config_manager.R", encoding = "UTF-8")
  safe_source("utils/data_index_manager.R", encoding = "UTF-8")
  safe_source("utils/file_naming_utils.R", encoding = "UTF-8")
  # 加载数据库转换模块
  safe_source("utils/database_converter.R", encoding = "UTF-8")
  safe_source("utils/data_validator.R", encoding = "UTF-8")
  safe_source("utils/ms_matching_algorithm.R", encoding = "UTF-8")
  if (exists("log_info")) {
    log_info("项目管理工具函数加载成功")
  }
}, error = function(e) {
  if (exists("log_warning")) {
    log_warning(paste("项目管理工具函数加载失败:", e$message))
  }
})

# 路径管理函数已在path_manager.R中定义，这里不需要备用实现

if (!exists("get_project_root_path")) {
  get_project_root_path <- function() {
    # 在Shiny环境中，尝试多种方式获取项目路径

    # 方式1: 检查全局项目配置
    project_config_file <- "project_config.json"
    if (file.exists(project_config_file)) {
      tryCatch({
        config <- jsonlite::read_json(project_config_file)
        if (!is.null(config$current_project_path) && dir.exists(config$current_project_path)) {
          return(config$current_project_path)
        }
      }, error = function(e) {
        log_warning(paste("读取项目配置失败:", e$message))
      })
    }

    # 方式2: 检查当前目录是否是项目目录
    current_dir <- getwd()
    if (file.exists(file.path(current_dir, "project.json"))) {
      return(current_dir)
    }

    # 方式3: 检查是否有项目子目录
    possible_projects <- c("test/QC3", "projects/QC3")
    for (proj_path in possible_projects) {
      if (dir.exists(proj_path)) {
        return(proj_path)
      }
    }

    # 方式4: 如果在Shiny环境中，尝试从session获取
    if (exists("session") && !is.null(session)) {
      # 可以在这里添加从session获取项目信息的逻辑
    }

    return(NULL)
  }
}

if (!exists("load_data_index")) {
  load_data_index <- function() {
    project_root <- get_project_root_path()
    if (is.null(project_root)) {
      return(list(data_files = list()))
    }

    index_file <- file.path(project_root, "data", "data_index.json")
    if (file.exists(index_file)) {
      tryCatch({
        return(jsonlite::read_json(index_file, simplifyVector = FALSE))
      }, error = function(e) {
        log_warning(paste("读取数据索引失败:", e$message))
      })
    }

    return(list(data_files = list()))
  }
}

# 检查是否在Shiny环境中
is_shiny_env <- function() {
  # 检查多个Shiny相关函数的存在
  return(exists("reactiveVal") && is.function(reactiveVal) ||
         exists("observe") && is.function(observe) ||
         exists("session") && exists("input") && exists("output"))
}

# 项目信息更新触发器（如果不存在则创建）
if (!exists("project_info_updated")) {
  if (is_shiny_env()) {
    project_info_updated <- reactiveVal(0)
  } else {
    project_info_updated <- function() 0  # 非Shiny环境的占位符
  }
}

# 项目信息显示
if (is_shiny_env() && exists("output")) {
  output$project_info <- renderText({
  # 添加响应式依赖
  project_info_updated()
  tryCatch({
    # 使用路径管理器获取项目信息
    project_root <- get_project_root_path()
    
    if (is.null(project_root)) {
      return("当前没有活动项目\n请先创建或导入项目")
    }
    
    # 尝试读取项目配置文件
    config_file <- get_project_config_path()
    project_info <- NULL
    
    if (file.exists(config_file)) {
      tryCatch({
        config <- jsonlite::fromJSON(config_file)
        project_info <- list(
          name = config$name %||% "未知",
          path = config$path %||% project_root,
          created_time = config$created_time %||% "未知",
          description = config$description %||% "无描述",
          status = config$status %||% "未知"
        )
      }, error = function(e) {
        log_error(paste("读取项目配置失败:", e$message))
      })
    }
    
    # 如果没有读取到配置，尝试其他配置文件
    if (is.null(project_info)) {
      config_dir <- get_project_relative_path("config")
      if (dir.exists(config_dir)) {
        json_files <- list.files(config_dir, pattern = "\\.json$", full.names = TRUE)
        for (json_file in json_files) {
          tryCatch({
            config <- jsonlite::fromJSON(json_file)
            if (!is.null(config$name)) {
              project_info <- list(
                name = config$name,
                path = config$path %||% project_root,
                created_time = config$created_time %||% "未知",
                description = config$description %||% "无描述",
                status = config$status %||% "未知"
              )
              break
            }
          }, error = function(e) {})
        }
      }
    }
    
    # 获取项目统计信息
    data_count <- 0
    result_count <- 0
    report_count <- 0
    
    data_file <- get_data_list_path()
    if (file.exists(data_file)) {
      tryCatch({
        data_list <- read.csv(data_file, stringsAsFactors = FALSE)
        data_count <- nrow(data_list)
      }, error = function(e) {})
    }
    
    results_dir <- get_project_relative_path("results")
    if (dir.exists(results_dir)) {
      result_count <- length(list.files(results_dir))
    }
    
    reports_dir <- get_project_relative_path("reports")
    if (dir.exists(reports_dir)) {
      report_count <- length(list.files(reports_dir))
    }
    
    # 显示项目信息
    if (!is.null(project_info)) {
      info_text <- paste(
        paste("项目名称:", project_info$name),
        paste("项目路径:", project_info$path),
        paste("创建时间:", project_info$created_time),
        paste("项目描述:", project_info$description),
        paste("项目状态:", project_info$status),
        paste("数据文件:", data_count, "个"),
        paste("结果文件:", result_count, "个"),
        paste("报告文件:", report_count, "个"),
        paste("当前工作目录:", getwd()),
        paste("项目根路径:", project_root),
        paste("配置文件:", config_file),
        paste("系统时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S")),
        paste("应用版本:", GLOBAL_CONFIG$version),
        sep = "\n"
      )
      return(info_text)
    } else {
      return(paste(
        "项目配置文件读取失败",
        paste("项目根路径:", project_root),
        paste("当前工作目录:", getwd()),
        paste("配置文件路径:", config_file),
        paste("配置文件存在:", file.exists(config_file)),
        paste("系统时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S")),
        sep = "\n"
      ))
    }
  }, error = function(e) {
    return(paste("获取项目信息失败:", e$message, "\n当前工作目录:", getwd()))
  })
  })
}

# 更新当前项目名称
observe({
  project_root <- get_project_root_path()
  
  if (!is.null(project_root)) {
    config_file <- get_project_config_path()
    
    if (file.exists(config_file)) {
      tryCatch({
        config <- jsonlite::fromJSON(config_file)
        project_name <- config$name %||% basename(project_root)
        
        # 更新UI中的项目名称
        runjs(paste0("
          var projectNameEl = document.getElementById('current_project_name');
          if (projectNameEl) projectNameEl.textContent = '", project_name, "';
        "))
        
      }, error = function(e) {
        # 如果读取失败，使用目录名
        project_name <- basename(project_root)
        runjs(paste0("
          var projectNameEl = document.getElementById('current_project_name');
          if (projectNameEl) projectNameEl.textContent = '", project_name, "';
        "))
      })
    }
  }
})

# 数据管理相关逻辑已移至 data_management_server.R

# 以下数据管理代码已移至专门的 data_management_server.R 文件
# 包括：数据导入、编辑、删除、导出等功能

# 监控参数相关逻辑

# ===== 监控离子管理 =====

# 监控离子数据（包含ID列）
if (is_shiny_env()) {
  monitor_ions_data <- reactiveVal(data.frame(
    ID = character(0),
    化合物名称 = character(0),
    分子质量 = numeric(0),
    保留时间 = numeric(0),
    离子化模式 = character(0),
    离子质荷比 = numeric(0),
    离子类型 = character(0),
    MS级别 = character(0),
    备注 = character(0),
    stringsAsFactors = FALSE
  ))

  # 编辑状态管理
  editing_ion_id <- reactiveVal(NULL)  # 当前编辑的离子ID
} else {
  # 非Shiny环境的占位符
  monitor_ions_data <- function() data.frame()
  editing_ion_id <- function() NULL
}

# 监控离子数据自动加载（响应项目变化）
observe({
  # 监听项目路径变化，当项目导入或切换时自动加载监控离子配置
  project_root <- get_project_root_path()

  if (!is.null(project_root) && project_root != "") {
    tryCatch({
      # 检查监控离子配置文件是否存在
      ions_config_path <- file.path(project_root, "data", "monitor_ions_data.yaml")

      if (file.exists(ions_config_path)) {
        log_info(paste("检测到项目监控离子配置文件:", ions_config_path))

        # 读取YAML配置文件
        ions_config <- yaml::read_yaml(ions_config_path, fileEncoding = "UTF-8")

        if (!is.null(ions_config$monitor_ions_data) && length(ions_config$monitor_ions_data) > 0) {
          # 转换为数据框格式
          ions_list <- ions_config$monitor_ions_data

          # 构建数据框
          new_data <- data.frame(
            ID = character(0),
            化合物名称 = character(0),
            分子质量 = numeric(0),
            保留时间 = numeric(0),
            离子化模式 = character(0),
            离子质荷比 = numeric(0),
            离子类型 = character(0),
            MS级别 = character(0),
            备注 = character(0),
            stringsAsFactors = FALSE
          )

          for (i in seq_along(ions_list)) {
            ion <- ions_list[[i]]
            new_row <- data.frame(
              ID = ion$ID %||% paste0("ion_", i, "_", as.numeric(Sys.time())),
              化合物名称 = ion$化合物名称 %||% "",
              分子质量 = as.numeric(ion$分子质量 %||% 0),
              保留时间 = as.numeric(ion$保留时间 %||% 0),
              离子化模式 = ion$离子化模式 %||% "",
              离子质荷比 = as.numeric(ion$离子质荷比 %||% 0),
              离子类型 = ion$离子类型 %||% "",
              MS级别 = ion$MS级别 %||% "MS1",
              备注 = ion$备注 %||% "",
              stringsAsFactors = FALSE
            )
            new_data <- rbind(new_data, new_row)
          }
          monitor_ions_data(new_data)
          log_info(paste("自动加载了", nrow(new_data), "个监控离子配置"))

          if (is_shiny_env()) {
            showNotification(
              paste("已自动加载", nrow(new_data), "个监控离子配置"),
              type = "message",
              duration = 3
            )
          }
        } else {
          monitor_ions_data(data.frame(
            ID = character(0),
            化合物名称 = character(0),
            分子质量 = numeric(0),
            保留时间 = numeric(0),
            离子化模式 = character(0),
            离子质荷比 = numeric(0),
            离子类型 = character(0),
            MS级别 = character(0),
            备注 = character(0),
            stringsAsFactors = FALSE
          ))
          log_info("监控离子配置文件为空，初始化为空数据")
        }
      } else {
        monitor_ions_data(data.frame(
          ID = character(0),
          化合物名称 = character(0),
          分子质量 = numeric(0),
          保留时间 = numeric(0),
          离子化模式 = character(0),
          离子质荷比 = numeric(0),
          离子类型 = character(0),
          MS级别 = character(0),
          备注 = character(0),
          stringsAsFactors = FALSE
        ))
        log_debug("未找到监控离子配置文件，初始化为空数据")
      }

    }, error = function(e) {
      log_error(paste("自动加载监控离子数据失败:", e$message))
      # 出错时也要初始化为空数据，避免界面异常
      monitor_ions_data(data.frame(
        ID = character(0),
        化合物名称 = character(0),
        分子质量 = numeric(0),
        保留时间 = numeric(0),
        离子化模式 = character(0),
        离子质荷比 = numeric(0),
        离子类型 = character(0),
        MS级别 = character(0),
        备注 = character(0),
        stringsAsFactors = FALSE
      ))
    })
  } else {
    # 没有活动项目时，清空监控离子数据
    monitor_ions_data(data.frame(
      ID = character(0),
      化合物名称 = character(0),
      分子质量 = numeric(0),
      保留时间 = numeric(0),
      离子化模式 = character(0),
      离子质荷比 = numeric(0),
      离子类型 = character(0),
      MS级别 = character(0),
      备注 = character(0),
      stringsAsFactors = FALSE
    ))
    log_debug("无活动项目，清空监控离子数据")
  }
})

# 监控离子统计信息
output$total_compounds_count <- renderText({
  data <- monitor_ions_data()
  if (nrow(data) > 0) {
    length(unique(data$化合物名称))
  } else {
    "0"
  }
})

output$total_ions_count <- renderText({
  nrow(monitor_ions_data())
})

output$scan_modes_count <- renderText({
  data <- monitor_ions_data()
  if (nrow(data) > 0) {
    modes <- unique(data$扫描模式)
    paste(modes[modes != ""], collapse = ", ")
  } else {
    "无"
  }
})

output$config_status <- renderText({
  data <- monitor_ions_data()
  if (nrow(data) > 0) {
    "已配置"
  } else {
    "未配置"
  }
})

# 监控离子表格
output$monitor_ions_table <- DT::renderDataTable({
  data <- monitor_ions_data()

  # 隐藏ID列，但保留用于内部处理
  display_data <- data
  if ("ID" %in% colnames(display_data)) {
    display_data <- display_data[, !colnames(display_data) %in% "ID", drop = FALSE]
  }
  # 移除扫描模式列（如果还存在）
  if ("扫描模式" %in% colnames(display_data)) {
    display_data <- display_data[, !colnames(display_data) %in% "扫描模式", drop = FALSE]
  }

  DT::datatable(
    display_data,
    selection = "multiple",
    options = list(
      pageLength = 15,
      scrollX = TRUE,
      language = list(
        url = "//cdn.datatables.net/plug-ins/1.10.11/i18n/Chinese.json"
      )
    )
  )
})

# 清空表单按钮
observeEvent(input$clear_form, {
  tryCatch({
    # 重置表单
    updateTextInput(session, "compound_name", value = "")
    updateNumericInput(session, "molecular_weight", value = NA)
    updateNumericInput(session, "retention_time", value = NA)
    updateSelectInput(session, "scan_mode", selected = "DDA")
    updateSelectInput(session, "ionization_mode", selected = "positive")
    updateCheckboxInput(session, "auto_calculate_mz", value = TRUE)
    updateNumericInput(session, "ion_mz", value = NA)
    updateTextAreaInput(session, "ion_notes", value = "分子离子峰监控")

    # 重置编辑状态
    editing_ion_id(NULL)

    showNotification("表单已清空", type = "default")

  }, error = function(e) {
    showNotification(paste("清空表单失败:", e$message), type = "error")
    log_error(paste("清空表单错误:", e$message))
  })
})

# 编辑选中离子
observeEvent(input$edit_selected_ion, {
  selected <- input$monitor_ions_table_rows_selected
  if (length(selected) == 0) {
    showNotification("请先选择要编辑的离子", type = "warning")
    return()
  }

  if (length(selected) > 1) {
    showNotification("一次只能编辑一个离子", type = "warning")
    return()
  }

  tryCatch({
    data <- monitor_ions_data()
    row <- data[selected, ]

    # 设置编辑状态
    editing_ion_id(row$ID)

    # 填充表单数据
    updateTextInput(session, "compound_name", value = row$化合物名称)
    updateNumericInput(session, "molecular_weight", value = row$分子质量)
    updateNumericInput(session, "retention_time", value = row$保留时间)
    updateSelectInput(session, "scan_mode", selected = row$扫描模式)
    updateSelectInput(session, "ionization_mode", selected = row$离子化模式)
    updateNumericInput(session, "ion_mz", value = row$离子质荷比)
    updateTextAreaInput(session, "ion_notes", value = row$备注)

    # 暂时关闭自动计算，避免覆盖编辑的数据
    updateCheckboxInput(session, "auto_calculate_mz", value = FALSE)

    showNotification(paste("正在编辑离子:", row$化合物名称, row$离子类型), type = "default")

  }, error = function(e) {
    showNotification(paste("编辑离子失败:", e$message), type = "error")
    log_error(paste("编辑监控离子错误:", e$message))
  })
})

# 删除选中离子
observeEvent(input$remove_selected_ions, {
  selected <- input$monitor_ions_table_rows_selected
  if (length(selected) == 0) {
    showNotification("请先选择要删除的离子", type = "warning")
    return()
  }

  tryCatch({
    data <- monitor_ions_data()
    new_data <- data[-selected, ]
    monitor_ions_data(new_data)

    # 保存到项目文件
    if (exists("save_project_monitor_ions_data")) {
      save_project_monitor_ions_data(new_data)
    }

    showNotification(paste("已删除", length(selected), "个离子"), type = "default")
    log_info(paste("删除了", length(selected), "个监控离子"))

  }, error = function(e) {
    showNotification(paste("删除离子失败:", e$message), type = "error")
    log_error(paste("删除监控离子错误:", e$message))
  })
})

# 手动刷新监控离子列表（保留用于手动刷新按钮）
observeEvent(input$refresh_monitor_ions, {
  tryCatch({
    project_root <- get_project_root_path()
    if (is.null(project_root)) {
      showNotification("请先创建或导入项目", type = "warning")
      return()
    }

    # 强制重新触发自动加载机制
    # 通过重新读取配置文件来刷新数据
    ions_config_path <- file.path(project_root, "data", "monitor_ions_data.yaml")

    if (file.exists(ions_config_path)) {
      # 读取并重新加载配置
      ions_config <- yaml::read_yaml(ions_config_path, fileEncoding = "UTF-8")

      if (!is.null(ions_config$monitor_ions_data) && length(ions_config$monitor_ions_data) > 0) {
        ions_list <- ions_config$monitor_ions_data

        new_data <- data.frame(
          ID = character(0),
          化合物名称 = character(0),
          分子质量 = numeric(0),
          保留时间 = numeric(0),
          扫描模式 = character(0),
          离子类型 = character(0),
          离子质荷比 = numeric(0),
          stringsAsFactors = FALSE
        )

        for (i in seq_along(ions_list)) {
          ion <- ions_list[[i]]
          new_row <- data.frame(
            ID = ion$ID %||% paste0("ion_", i, "_", as.numeric(Sys.time())),
            化合物名称 = ion$化合物名称 %||% "",
            分子质量 = as.numeric(ion$分子质量 %||% 0),
            保留时间 = as.numeric(ion$保留时间 %||% 0),
            扫描模式 = ion$扫描模式 %||% "",
            离子类型 = ion$离子类型 %||% "",
            离子质荷比 = as.numeric(ion$离子质荷比 %||% 0),
            stringsAsFactors = FALSE
          )
          new_data <- rbind(new_data, new_row)
        }

        monitor_ions_data(new_data)
        # 重置编辑状态
        editing_ion_id(NULL)
        showNotification(paste("监控离子列表已刷新，加载了", nrow(new_data), "个离子"), type = "default")
        log_info(paste("手动刷新监控离子列表，加载了", nrow(new_data), "个离子"))
      } else {
        monitor_ions_data(data.frame(
          ID = character(0),
          化合物名称 = character(0),
          分子质量 = numeric(0),
          保留时间 = numeric(0),
          扫描模式 = character(0),
          离子类型 = character(0),
          离子质荷比 = numeric(0),
          stringsAsFactors = FALSE
        ))
        showNotification("监控离子配置文件为空", type = "warning")
      }
    } else {
      showNotification("未找到监控离子配置文件", type = "warning")
    }

  }, error = function(e) {
    showNotification(paste("刷新失败:", e$message), type = "error")
    log_error(paste("手动刷新监控离子列表错误:", e$message))
  })
})

# YAML文件导入按钮
observeEvent(input$import_yaml_btn, {
  if (is.null(input$import_yaml_file)) {
    showNotification("请先选择YAML文件", type = "warning")
    return()
  }

  tryCatch({
    file_path <- input$import_yaml_file$datapath
    file_name <- input$import_yaml_file$name

    # 检查文件扩展名
    if (!grepl("\\.(yaml|yml)$", file_name, ignore.case = TRUE)) {
      showNotification("请选择YAML格式文件 (.yaml 或 .yml)", type = "error")
      return()
    }

    # 读取YAML文件
    yaml_content <- yaml::read_yaml(file_path, fileEncoding = "UTF-8")

    # 验证YAML结构（支持两种格式）
    monitor_ions_list <- NULL

    if (!is.null(yaml_content$monitor_ions_data)) {
      # 新格式：项目生成的简洁格式
      monitor_ions_list <- yaml_content$monitor_ions_data
      log_info("检测到项目生成的简洁YAML格式")
    } else if (!is.null(yaml_content$monitor_ions)) {
      # 旧格式：复杂的嵌套格式（保持兼容性）
      monitor_ions_list <- list()
      for (compound in yaml_content$monitor_ions) {
        if (length(compound$monitor_ions) > 0) {
          for (ion in compound$monitor_ions) {
            item <- list(
              化合物名称 = compound$compound_name,
              分子质量 = compound$molecular_weight,
              保留时间 = compound$retention_time,
              扫描模式 = compound$scan_mode,
              离子化模式 = compound$ionization_mode,
              离子质荷比 = ion$mz,
              离子类型 = ion$ion_type,
              MS级别 = paste0("MS", ion$ms_level %||% 1),
              备注 = ion$notes
            )
            monitor_ions_list[[length(monitor_ions_list) + 1]] <- item
          }
        }
      }
      log_info("检测到旧版嵌套YAML格式")
    } else {
      showNotification("YAML文件格式错误：缺少 monitor_ions_data 或 monitor_ions 节点", type = "error")
      return()
    }

    if (length(monitor_ions_list) == 0) {
      showNotification("YAML文件中没有找到监控离子数据", type = "warning")
      return()
    }

    # 转换为表格数据，支持字段缺失和自动计算
    rows <- list()
    skipped_count <- 0

    for (item in monitor_ions_list) {
      # 验证必填字段
      if (is.null(item$化合物名称) || item$化合物名称 == "") {
        skipped_count <- skipped_count + 1
        next  # 跳过缺少化合物名称的记录
      }

      if (is.null(item$分子质量) || is.na(item$分子质量) || item$分子质量 <= 0) {
        skipped_count <- skipped_count + 1
        next  # 跳过缺少分子质量的记录
      }

      ionization_mode <- item$离子化模式 %||% "positive"
      if (ionization_mode == "") {
        skipped_count <- skipped_count + 1
        next  # 跳过缺少离子化模式的记录
      }

      # 自动确定离子类型（如果缺失）
      ion_type <- item$离子类型
      if (is.null(ion_type) || ion_type == "") {
        ion_type <- if (ionization_mode == "positive") "[M+H]+" else "[M-H]-"
        log_debug(paste("自动确定离子类型:", ion_type, "for", item$化合物名称))
      }

      # 自动计算质荷比（如果缺失）
      ion_mz <- item$离子质荷比
      if (is.null(ion_mz) || is.na(ion_mz) || ion_mz <= 0) {
        # 简单计算
        ion_mz <- if (ion_type == "[M+H]+") {
          item$分子质量 + 1.007276
        } else if (ion_type == "[M-H]-") {
          item$分子质量 - 1.007276
        } else {
          item$分子质量
        }
        log_debug(paste("自动计算质荷比:", ion_mz, "for", item$化合物名称))
      }

      row <- list(
        化合物名称 = item$化合物名称,
        分子质量 = item$分子质量,
        保留时间 = item$保留时间 %||% 0,
        扫描模式 = item$扫描模式 %||% "DDA",
        离子化模式 = ionization_mode,
        离子质荷比 = ion_mz,
        离子类型 = ion_type,
        MS级别 = item$MS级别 %||% "MS1",
        备注 = item$备注 %||% "导入的监控离子"
      )
      rows[[length(rows) + 1]] <- row
    }

    if (skipped_count > 0) {
      log_info(paste("跳过了", skipped_count, "个缺少必填字段的记录"))
    }

    if (length(rows) > 0) {
      new_data <- do.call(rbind, lapply(rows, data.frame, stringsAsFactors = FALSE))

      # 为导入的数据添加ID
      if (!"ID" %in% colnames(new_data)) {
        new_data$ID <- paste0("ion_", seq_len(nrow(new_data)), "_", as.numeric(Sys.time()))
      }

      monitor_ions_data(new_data)

      # 保存到项目文件
      if (exists("save_project_monitor_ions_data")) {
        save_project_monitor_ions_data(new_data)
      }

      success_msg <- paste("成功导入", nrow(new_data), "个监控离子配置")
      if (skipped_count > 0) {
        success_msg <- paste(success_msg, "（跳过", skipped_count, "个无效记录）")
      }
      showNotification(success_msg, type = "default")
      log_info(paste("从", file_name, "导入了", nrow(new_data), "个监控离子，跳过", skipped_count, "个无效记录"))
    } else {
      showNotification("YAML文件中没有找到有效的监控离子配置", type = "warning")
    }

  }, error = function(e) {
    showNotification(paste("导入YAML文件失败:", e$message), type = "error")
    log_error(paste("YAML导入错误:", e$message))
  })
})

# YAML文件导出
output$export_monitor_yaml <- downloadHandler(
  filename = function() {
    paste("monitor_ions_", Sys.Date(), ".yaml", sep = "")
  },
  content = function(file) {
    tryCatch({
      data <- monitor_ions_data()

      if (nrow(data) == 0) {
        # 创建空配置
        yaml_content <- list(
          monitor_ions = list(),
          global_settings = list(
            default_tolerance = 0.01,
            retention_time_window = 0.5,
            min_intensity = 1000,
            max_intensity = 1000000,
            default_ionization_mode = "positive"
          ),
          instrument_settings = list(
            mass_analyzer = "Orbitrap",
            ionization_mode = "ESI",
            scan_type = "DDA",
            ms1_resolution = 70000,
            ms2_resolution = 17500,
            collision_energy_mode = "HCD"
          ),
          metadata = list(
            version = "2.0",
            created_date = as.character(Sys.Date()),
            created_by = "实验室实时质控系统",
            description = "DDA模式监控离子配置"
          )
        )
      } else {
        # 按化合物分组
        compounds <- list()
        compound_names <- unique(data$化合物名称)

        for (compound_name in compound_names) {
          compound_data <- data[data$化合物名称 == compound_name, ]
          first_row <- compound_data[1, ]

          # 构建监控离子列表
          monitor_ions <- list()
          for (i in 1:nrow(compound_data)) {
            row <- compound_data[i, ]
            ion <- list(
              mz = row$离子质荷比,
              ion_type = row$离子类型,
              ms_level = as.numeric(gsub("MS", "", row$MS级别)),
              notes = row$备注
            )
            monitor_ions[[length(monitor_ions) + 1]] <- ion
          }

          # 构建化合物配置
          compound <- list(
            compound_name = compound_name,
            molecular_weight = first_row$分子质量,
            retention_time = first_row$保留时间,
            scan_mode = first_row$扫描模式,
            ionization_mode = first_row$离子化模式,
            monitor_ions = monitor_ions
          )

          compounds[[length(compounds) + 1]] <- compound
        }

        yaml_content <- list(
          monitor_ions = compounds,
          global_settings = list(
            default_tolerance = 0.01,
            retention_time_window = 0.5,
            min_intensity = 1000,
            max_intensity = 1000000,
            default_ionization_mode = "positive"
          ),
          instrument_settings = list(
            mass_analyzer = "Orbitrap",
            ionization_mode = "ESI",
            scan_type = "DDA",
            ms1_resolution = 70000,
            ms2_resolution = 17500,
            collision_energy_mode = "HCD"
          ),
          metadata = list(
            version = "2.0",
            created_date = as.character(Sys.Date()),
            created_by = "实验室实时质控系统",
            description = "DDA模式监控离子配置",
            total_compounds = length(compound_names),
            total_ions = nrow(data)
          )
        )
      }

      # 写入YAML文件
      yaml::write_yaml(yaml_content, file, fileEncoding = "UTF-8")

      log_info(paste("导出监控离子配置到:", basename(file)))

    }, error = function(e) {
      log_error(paste("导出YAML文件错误:", e$message))
      stop("导出失败: ", e$message)
    })
  }
)

# 保存监控离子配置
observeEvent(input$save_monitor_ion, {
  tryCatch({
    # 调试信息
    log_debug(paste("保存监控离子 - 化合物名称:", input$compound_name))
    log_debug(paste("保存监控离子 - 分子质量:", input$molecular_weight))
    log_debug(paste("保存监控离子 - 离子质荷比:", input$ion_mz))
    log_debug(paste("保存监控离子 - 离子质荷比类型:", class(input$ion_mz)))

    # 验证必填字段
    if (is.null(input$compound_name) || input$compound_name == "") {
      showNotification("请输入化合物名称", type = "warning")
      return()
    }

    if (is.null(input$molecular_weight) || is.na(input$molecular_weight)) {
      showNotification("请输入分子质量", type = "warning")
      return()
    }

    # 更严格的质荷比验证
    if (is.null(input$ion_mz) || is.na(input$ion_mz) || input$ion_mz <= 0) {
      log_debug(paste("质荷比验证失败 - 值:", input$ion_mz, "是否为NULL:", is.null(input$ion_mz), "是否为NA:", is.na(input$ion_mz)))
      showNotification("请输入有效的离子质荷比", type = "warning")
      return()
    }

    # 根据离子化模式确定离子类型
    ion_type <- if (input$ionization_mode == "positive") "[M+H]+" else "[M-H]-"

    current_data <- monitor_ions_data()
    current_editing_id <- editing_ion_id()

    if (!is.null(current_editing_id)) {
      # 编辑模式：更新现有记录
      for (i in 1:nrow(current_data)) {
        if (current_data$ID[i] == current_editing_id) {
          current_data$化合物名称[i] <- input$compound_name
          current_data$分子质量[i] <- input$molecular_weight
          current_data$保留时间[i] <- input$retention_time %||% 0
          current_data$扫描模式[i] <- input$scan_mode %||% "DDA"
          current_data$离子化模式[i] <- input$ionization_mode %||% "positive"
          current_data$离子质荷比[i] <- input$ion_mz
          current_data$离子类型[i] <- ion_type
          current_data$MS级别[i] <- "MS1"
          current_data$备注[i] <- input$ion_notes %||% "分子离子峰监控"
          break
        }
      }
      updated_data <- current_data
      action_msg <- "监控离子已更新"
      log_msg <- paste("更新了监控离子:", input$compound_name, ion_type)
    } else {
      # 新增模式：添加新记录
      new_id <- paste0("ion_", as.numeric(Sys.time()) * 1000)  # 使用时间戳生成唯一ID

      new_row <- data.frame(
        ID = new_id,
        化合物名称 = input$compound_name,
        分子质量 = input$molecular_weight,
        保留时间 = input$retention_time %||% 0,
        扫描模式 = input$scan_mode %||% "DDA",
        离子化模式 = input$ionization_mode %||% "positive",
        离子质荷比 = input$ion_mz,
        离子类型 = ion_type,
        MS级别 = "MS1",
        备注 = input$ion_notes %||% "分子离子峰监控",
        stringsAsFactors = FALSE
      )

      updated_data <- rbind(current_data, new_row)
      action_msg <- "监控离子已添加"
      log_msg <- paste("添加了监控离子:", input$compound_name, ion_type)
    }

    # 更新数据
    monitor_ions_data(updated_data)

    # 保存到项目文件
    if (exists("save_project_monitor_ions_data")) {
      save_project_monitor_ions_data(updated_data)
    }

    # 清空表单和重置编辑状态
    updateTextInput(session, "compound_name", value = "")
    updateNumericInput(session, "molecular_weight", value = NA)
    updateNumericInput(session, "retention_time", value = NA)
    updateNumericInput(session, "ion_mz", value = NA)
    updateTextAreaInput(session, "ion_notes", value = "分子离子峰监控")
    updateCheckboxInput(session, "auto_calculate_mz", value = TRUE)
    editing_ion_id(NULL)

    showNotification(action_msg, type = "default")
    log_info(log_msg)

  }, error = function(e) {
    showNotification(paste("保存监控离子失败:", e$message), type = "error")
    log_error(paste("保存监控离子错误:", e$message))
  })
})

# 空值合并操作符
`%||%` <- function(x, y) {
  if (is.null(x) || length(x) == 0 || (is.character(x) && x == "")) y else x
}

# ===== 质控监控模块集成 =====
# 质控监控相关模块已移除，专注于数据管理和监控参数配置

# QC控制器相关功能已移除

# 自动刷新监控参数
observe({
  project_root <- get_project_root_path()
  if (!is.null(project_root)) {
    # 自动导入已配置的监控离子
    tryCatch({
      ions_config_path <- file.path(project_root, "data", "monitor_ions_data.yaml")
      if (file.exists(ions_config_path)) {
        ions_config <- yaml::read_yaml(ions_config_path)
        if (!is.null(ions_config$monitor_ions_data)) {
          ion_mz_values <- sapply(ions_config$monitor_ions_data, function(x) x$离子质荷比)
          ion_mz_string <- paste(ion_mz_values, collapse = ", ")

          # 更新监控离子输入框
          if (is_shiny_env()) {
            updateTextAreaInput(session, "monitor_ions_input",
                               value = ion_mz_string)
          }

          log_info(paste("自动导入监控离子:", length(ion_mz_values), "个"))
        }
      }
    }, error = function(e) {
      log_warning(paste("自动导入监控离子失败:", e$message))
    })
  }
})

# ===== 实时统计面板 =====



# 统计输出（带错误处理和调试信息）






# 简化的统计输出 - 统一进度指示器
output$processing_progress_percent <- renderText({
  tryCatch({
    total_count <- total_entries_reactive()
    db_count <- database_count_reactive()

    if (total_count == 0) {
      return("0%")
    }

    # 简化的整体进度计算：基于最终数据库存储完成度
    overall_progress <- (db_count / total_count) * 100
    paste0(round(overall_progress), "%")
  }, error = function(e) {
    return("0%")
  })
})



# 创建响应式值来存储文件计数

database_count_reactive <- reactive({
  tryCatch({
    project_path <- get_project_root_path()
    if (is.null(project_path) || !dir.exists(project_path)) {
      return(0)
    }

    db_files <- list.files(project_path, pattern = "\\.db$", recursive = TRUE, full.names = TRUE)
    spectra_db_files <- db_files[grepl("spectra", basename(db_files), ignore.case = TRUE)]
    return(length(spectra_db_files))
  }, error = function(e) {
    return(0)
  })
})

total_entries_reactive <- reactive({
  tryCatch({
    index <- load_data_index()
    return(length(index$data_files))
  }, error = function(e) {
    return(0)
  })
})

# 统一进度状态输出
output$conversion_status <- renderText({
  tryCatch({
    if (conversion_in_progress() || database_conversion_in_progress()) {
      return("转换进行中...")
    }

    total_count <- total_entries_reactive()
    db_count <- database_count_reactive()

    if (total_count == 0) {
      return("待开始")
    } else if (db_count == 0) {
      return("未开始")
    } else if (db_count < total_count) {
      return(paste0("进行中 (", db_count, "/", total_count, ")"))
    } else {
      return("转换完成")
    }
  }, error = function(e) {
    return("状态未知")
  })
})







# 自动刷新间隔管理
current_refresh_interval <- reactiveVal(5)  # 默认5分钟
auto_refresh_enabled <- reactiveVal(FALSE)   # 默认禁用自动刷新（与UI一致）
timer_restart_trigger <- reactiveVal(0)     # 用于触发定时器重启

# 自动转换管理
auto_conversion_enabled <- reactiveVal(FALSE)
auto_conversion_in_progress <- reactiveVal(FALSE)
file_stability_seconds <- reactiveVal(30)
file_stability_tracker <- reactiveVal(list())

# 日志节流管理（防止重复日志）- 使用环境变量而非reactiveVal
if (!exists("log_throttle_cache_env")) {
  log_throttle_cache_env <<- new.env()
}
if (!exists("last_conversion_check_summary_env")) {
  last_conversion_check_summary_env <<- new.env()
}

# 智能日志函数（带节流）- 修复版
log_throttled <- function(level, message, key, throttle_seconds = 300) {
  tryCatch({
    current_time <- Sys.time()

    # 检查是否需要节流
    if (exists(key, envir = log_throttle_cache_env)) {
      last_time <- get(key, envir = log_throttle_cache_env)
      time_diff <- as.numeric(difftime(current_time, last_time, units = "secs"))

      if (time_diff < throttle_seconds) {
        return(FALSE)  # 被节流，不记录日志
      }
    }

    # 记录日志并更新缓存
    assign(key, current_time, envir = log_throttle_cache_env)

    # 根据级别记录日志
    if (level == "debug") {
      log_debug(message)
    } else if (level == "info") {
      log_info(message)
    } else if (level == "warning") {
      log_warning(message)
    }

    return(TRUE)  # 日志已记录
  }, error = function(e) {
    # 如果节流系统出错，仍然记录原始日志
    if (level == "debug") {
      log_debug(message)
    } else if (level == "info") {
      log_info(message)
    } else if (level == "warning") {
      log_warning(message)
    }
    return(TRUE)
  })
}

# 清理过期的日志节流缓存
cleanup_log_throttle_cache <- function() {
  tryCatch({
    current_time <- Sys.time()
    keys_to_remove <- c()

    # 检查所有缓存项
    for (key in ls(envir = log_throttle_cache_env)) {
      last_time <- get(key, envir = log_throttle_cache_env)
      time_diff <- as.numeric(difftime(current_time, last_time, units = "secs"))

      # 清理超过1小时的缓存项
      if (time_diff >= 3600) {
        keys_to_remove <- c(keys_to_remove, key)
      }
    }

    # 移除过期项
    for (key in keys_to_remove) {
      rm(list = key, envir = log_throttle_cache_env)
    }

  }, error = function(e) {
    # 清理失败时重置环境
    rm(list = ls(envir = log_throttle_cache_env), envir = log_throttle_cache_env)
  })
}

# 验证刷新间隔输入的函数
validate_refresh_interval <- function(value) {
  if (is.null(value) || is.na(value) || value == "" || !is.numeric(value)) {
    return(list(valid = FALSE, message = "请输入有效的数值"))
  }

  if (value < 1) {
    return(list(valid = FALSE, message = "刷新间隔不能少于1分钟"))
  }

  if (value > 60) {
    return(list(valid = FALSE, message = "刷新间隔不能超过60分钟"))
  }

  return(list(valid = TRUE, message = ""))
}

# 自动刷新启用/禁用事件
observeEvent(input$enable_auto_refresh, {
  auto_refresh_enabled(input$enable_auto_refresh %||% TRUE)

  if (auto_refresh_enabled()) {
    log_info("自动刷新统计已启用")
    current_interval <- current_refresh_interval()
    session$sendCustomMessage("updateAutoRefreshStatus", list(
      status = paste0("已启用 (", current_interval, "分钟)"),
      enabled = TRUE
    ))
    # 触发定时器重启
    timer_restart_trigger(timer_restart_trigger() + 1)
  } else {
    log_info("自动刷新统计已禁用")
    session$sendCustomMessage("updateAutoRefreshStatus", list(
      status = "已禁用",
      enabled = FALSE
    ))
  }
}, ignoreInit = TRUE)

# 自动刷新间隔变化事件
observeEvent(input$auto_refresh_interval, {
  tryCatch({
    new_interval <- input$auto_refresh_interval
    validation <- validate_refresh_interval(new_interval)

    if (validation$valid) {
      # 验证通过，应用新间隔
      current_refresh_interval(as.numeric(new_interval))

      # 隐藏验证消息
      session$sendCustomMessage("hideValidation", list())

      # 更新状态显示
      if (auto_refresh_enabled()) {
        session$sendCustomMessage("updateAutoRefreshStatus", list(
          status = paste0("已启用 (", new_interval, "分钟)"),
          enabled = TRUE
        ))
      } else {
        session$sendCustomMessage("updateAutoRefreshStatus", list(
          status = "已禁用",
          enabled = FALSE
        ))
      }

      # 显示成功通知
      if (is_shiny_env()) {
        showNotification(
          paste("自动刷新间隔已设置为", new_interval, "分钟"),
          type = "message",
          duration = 3
        )
      }

      log_info(paste("用户设置自动刷新间隔为", new_interval, "分钟"))

        # 重启自动刷新定时器（如果已启用）
        if (auto_refresh_enabled()) {
          timer_restart_trigger(timer_restart_trigger() + 1)
        }

    } else {
      # 验证失败，显示错误消息
      session$sendCustomMessage("showValidation", list(
        message = validation$message
      ))
    }

  }, error = function(e) {
    log_error(paste("应用刷新间隔失败:", e$message))
    session$sendCustomMessage("showValidation", list(
      message = "设置失败，请重试"
    ))
  })
})

# 可配置的定期自动刷新统计（修复版）
observe({
  # 监听定时器重启触发器
  timer_restart_trigger()

  # 获取当前设置的刷新间隔
  interval_minutes <- current_refresh_interval()

  # 确保间隔有效
  if (is.null(interval_minutes) || is.na(interval_minutes) || interval_minutes < 1) {
    interval_minutes <- 5  # 默认值
  }

  # 转换为毫秒
  interval_ms <- interval_minutes * 60 * 1000

  # 设置定时器
  invalidateLater(interval_ms, session)

  # 只有在启用自动刷新且有活动项目时才刷新
  if (auto_refresh_enabled()) {
    project_root <- get_project_root_path()
    if (!is.null(project_root) && project_root != "") {

      # 如果启用了自动转换，智能检查需要转换的文件
      auto_enabled <- auto_conversion_enabled()
      auto_in_progress <- auto_conversion_in_progress()

      # 防止重复执行：使用时间戳检查
      current_time <- Sys.time()
      last_check_time <- NULL
      if (exists("last_auto_check_time", envir = last_conversion_check_summary_env)) {
        last_check_time <- get("last_auto_check_time", envir = last_conversion_check_summary_env)
      }

      # 确保检查间隔至少30秒，防止同一刷新周期内重复执行
      should_check <- TRUE
      if (!is.null(last_check_time)) {
        time_since_last_check <- as.numeric(difftime(current_time, last_check_time, units = "secs"))
        if (time_since_last_check < 30) {
          should_check <- FALSE
          log_throttled("debug", paste("跳过自动转换检查：距上次检查仅", round(time_since_last_check), "秒"),
                       "skip_auto_check_too_soon", 60)
        }
      }

      # 每小时清理一次日志缓存
      if (exists("last_cleanup_time", envir = last_conversion_check_summary_env)) {
        last_cleanup <- get("last_cleanup_time", envir = last_conversion_check_summary_env)
        cleanup_diff <- as.numeric(difftime(current_time, last_cleanup, units = "secs"))
        if (cleanup_diff > 3600) {
          cleanup_log_throttle_cache()
          assign("last_cleanup_time", current_time, envir = last_conversion_check_summary_env)
        }
      } else {
        assign("last_cleanup_time", current_time, envir = last_conversion_check_summary_env)
      }

      if (auto_enabled && !auto_in_progress && should_check) {
        # 更新最后检查时间
        assign("last_auto_check_time", current_time, envir = last_conversion_check_summary_env)

        # 使用节流日志记录检查开始
        log_throttled("debug", "=== 自动转换检查开始 ===", "auto_conversion_check_start", 180)

        tryCatch({
          # 智能检查：先检查是否有文件在稳定性跟踪中
          stability_tracker <- file_stability_tracker()
          has_tracking_files <- length(stability_tracker) > 0

          # 决定是否需要完整扫描
          should_full_scan <- TRUE

          if (exists("last_full_scan_time", envir = last_conversion_check_summary_env)) {
            last_scan_time <- get("last_full_scan_time", envir = last_conversion_check_summary_env)
            time_since_last <- as.numeric(difftime(current_time, last_scan_time, units = "secs"))

            # 如果没有跟踪文件且最近扫描过，可以跳过完整扫描
            if (!has_tracking_files && time_since_last < 300) {  # 5分钟内扫描过
              should_full_scan <- FALSE
              log_throttled("debug", "跳过完整扫描：无跟踪文件且最近已扫描", "skip_full_scan", 300)
            }
          }

          files_needing_conversion <- c()

          if (should_full_scan) {
            # 执行完整文件扫描
            files_needing_conversion <- get_files_needing_conversion()

            # 更新扫描时间
            assign("last_full_scan_time", current_time, envir = last_conversion_check_summary_env)
            assign("last_files_count", length(files_needing_conversion),
                  envir = last_conversion_check_summary_env)

            # 使用节流日志记录扫描结果
            if (length(files_needing_conversion) > 0) {
              log_info(paste("发现", length(files_needing_conversion), "个文件需要自动转换"))
              for (file_path in files_needing_conversion) {
                log_throttled("info", paste("需要转换:", basename(file_path)),
                             paste("needs_conversion", basename(file_path)), 600)
              }
            } else {
              log_throttled("debug", "完整扫描：所有文件都已转换", "all_files_converted", 600)
            }
          } else {
            # 轻量级检查：只检查正在跟踪稳定性的文件
            if (has_tracking_files) {
              log_throttled("debug", "执行轻量级稳定性检查", "lightweight_stability_check", 180)
              files_needing_conversion <- names(stability_tracker)
            }
          }

          # 如果有文件需要处理，启动转换
          if (length(files_needing_conversion) > 0) {
            # 获取数据库转换设置
            enable_db_conversion <- input$enable_database_conversion %||% TRUE
            auto_convert_files_by_status(files_needing_conversion, enable_db_conversion)
          }

        }, error = function(e) {
          log_warning(paste("自动转换检查失败:", e$message))
        })
      } else {
        # 使用节流日志记录跳过原因
        if (!auto_enabled) {
          log_throttled("debug", "自动转换未启用，跳过检查", "auto_conversion_disabled", 600)
        } else if (auto_in_progress) {
          log_throttled("debug", "自动转换正在进行中，跳过检查", "auto_conversion_in_progress", 60)
        }
      }
    } else {
      # 自动刷新未启用时，也不执行自动转换检查
      log_throttled("debug", "自动刷新未启用，跳过自动转换检查", "auto_refresh_disabled", 600)
    }
  }
})



# 初始化自动刷新状态显示
observe({
  # 确保在UI加载后更新显示
  if (!is.null(session)) {
    if (auto_refresh_enabled()) {
      session$sendCustomMessage("updateAutoRefreshStatus", list(
        status = paste0("已启用 (", current_refresh_interval(), "分钟)"),
        enabled = TRUE
      ))
    } else {
      session$sendCustomMessage("updateAutoRefreshStatus", list(
        status = "已禁用",
        enabled = FALSE
      ))
    }
  }
})

# 实时输入验证（可选，提供即时反馈）
observeEvent(input$auto_refresh_interval, {
  if (!is.null(input$auto_refresh_interval)) {
    validation <- validate_refresh_interval(input$auto_refresh_interval)

    if (!validation$valid) {
      session$sendCustomMessage("showValidation", list(
        message = validation$message
      ))
    } else {
      session$sendCustomMessage("hideValidation", list())
    }
  }
}, ignoreInit = TRUE)

# 自动转换启用/禁用事件
observeEvent(input$enable_auto_conversion, {
  auto_conversion_enabled(input$enable_auto_conversion %||% FALSE)

  if (auto_conversion_enabled()) {
    log_info("自动转换已启用")
    session$sendCustomMessage("updateAutoConversionStatus", list(status = "已启用"))
    log_info("自动转换将在下次刷新周期检查需要转换的文件")
  } else {
    log_info("自动转换已禁用")
    session$sendCustomMessage("updateAutoConversionStatus", list(status = "已禁用"))

    # 清理状态
    file_stability_tracker(list())
  }
}, ignoreInit = TRUE)

# 自动转换检查间隔设置
observeEvent(input$auto_conversion_check_interval, {
  if (!is.null(input$auto_conversion_check_interval) && is.numeric(input$auto_conversion_check_interval)) {
    check_interval <- max(10, min(300, input$auto_conversion_check_interval))
    file_stability_seconds(check_interval)  # 使用同一个变量存储
    log_info(paste("自动转换检查间隔设置为:", check_interval, "秒"))
  }
}, ignoreInit = TRUE)

# 文件稳定性检查时间设置（保持向后兼容）
observeEvent(input$file_stability_seconds, {
  if (!is.null(input$file_stability_seconds) && is.numeric(input$file_stability_seconds)) {
    stability_time <- max(10, min(300, input$file_stability_seconds))
    file_stability_seconds(stability_time)
    log_info(paste("文件稳定性检查时间设置为:", stability_time, "秒"))
  }
}, ignoreInit = TRUE)

# 获取需要转换的文件（基于转换状态检测）
get_files_needing_conversion <- function() {
  tryCatch({
    project_root <- get_project_root_path()
    if (is.null(project_root) || project_root == "") {
      log_debug("获取需要转换的文件: 项目路径为空")
      return(c())
    }

    log_debug(paste("获取需要转换的文件: 项目路径 =", project_root))

    # 获取数据索引（使用非响应式方法）
    data_list <- NULL

    # 尝试使用响应式函数（如果在响应式上下文中）
    if (exists("get_data_index_dataframe")) {
      tryCatch({
        data_list <- get_data_index_dataframe()
      }, error = function(e) {
        if (grepl("reactive context", e$message)) {
          log_debug("获取需要转换的文件: 不在响应式上下文中，使用备用方法")
          data_list <- NULL
        } else {
          stop(e)
        }
      })
    }

    # 如果响应式方法失败，使用直接读取索引文件的方法
    if (is.null(data_list) || !is.data.frame(data_list) || nrow(data_list) == 0) {
      log_debug("获取需要转换的文件: 使用备用方法读取数据索引")

      # 直接读取数据索引文件
      index <- tryCatch({
        load_data_index()
      }, error = function(e) {
        log_warning(paste("获取需要转换的文件: 加载数据索引失败:", e$message))
        list(data_files = list())
      })

      if (is.null(index) || !is.list(index) || length(index$data_files) == 0) {
        log_debug("获取需要转换的文件: 数据索引为空或无效")
        return(c())
      }

      # 将索引转换为数据框格式
      data_list <- data.frame(
        文件名 = sapply(index$data_files, function(x) x$file_name),
        路径 = sapply(index$data_files, function(x) x$file_path),
        stringsAsFactors = FALSE
      )
    }

    if (is.null(data_list) || !is.data.frame(data_list) || nrow(data_list) == 0) {
      log_debug("获取需要转换的文件: 最终数据索引为空或无效")
      return(c())
    }

    log_debug(paste("获取需要转换的文件: 数据索引行数 =", nrow(data_list)))

    files_needing_conversion <- c()
    cache_dir_v2 <- file.path(project_root, "data", "cache", "spectra_v2")

    for (i in 1:nrow(data_list)) {
      file_path <- data_list$路径[i]
      file_name <- data_list$文件名[i]

      # 只处理RAW文件
      if (!grepl("\\.(raw|d)$", file_path, ignore.case = TRUE)) {
        next
      }

      # 检查文件是否存在
      if (is.null(file_path) || file_path == "" || !file.exists(file_path)) {
        log_debug(paste("文件不存在，跳过:", ifelse(is.null(file_path), "NULL", file_path)))
        next
      }

      # 检查是否需要转换（基于缓存文件状态）
      project_name <- basename(project_root)
      cache_name <- generate_unique_cache_name(file_path, project_name)
      cache_file <- file.path(cache_dir_v2, paste0(cache_name, ".rds"))

      needs_conversion <- FALSE
      conversion_reason <- ""

      if (!file.exists(cache_file)) {
        needs_conversion <- TRUE
        conversion_reason <- "缓存文件不存在"
      } else {
        # 检查缓存文件是否有效
        cache_info <- file.info(cache_file)
        original_info <- file.info(file_path)

        if (is.na(cache_info$mtime) || is.na(original_info$mtime)) {
          needs_conversion <- TRUE
          conversion_reason <- "无法获取文件时间信息"
        } else if (cache_info$mtime <= original_info$mtime) {
          needs_conversion <- TRUE
          conversion_reason <- "缓存文件过旧"
        } else if (cache_info$size <= 1000) {
          needs_conversion <- TRUE
          conversion_reason <- "缓存文件过小"
        } else {
          # 缓存文件有效，但如果启用了自动转换，还需要检查数据库中是否存在
          auto_enabled <- auto_conversion_enabled()

          if (auto_enabled) {
            # 检查文件是否已存在于数据库中
            file_exists_in_db <- check_file_exists_in_database(cache_file)

            if (!file_exists_in_db) {
              needs_conversion <- TRUE
              conversion_reason <- "缓存文件存在但不在数据库中"
            }
          }
        }
      }

      if (needs_conversion) {
        files_needing_conversion <- c(files_needing_conversion, file_path)

        # 使用节流日志记录需要转换的文件
        conversion_key <- paste("needs_conversion_detail", file_name)
        log_throttled("debug",
                     paste("文件需要转换:", file_name, "-", conversion_reason),
                     conversion_key, 300)  # 5分钟节流
      } else {
        # 使用节流日志记录已转换的文件
        converted_key <- paste("already_converted", file_name)
        log_throttled("debug",
                     paste("文件已转换:", file_name),
                     converted_key, 600)  # 10分钟节流
      }
    }

    log_debug(paste("获取需要转换的文件: 找到", length(files_needing_conversion), "个需要转换的文件"))
    return(files_needing_conversion)

  }, error = function(e) {
    log_warning(paste("获取需要转换的文件失败:", e$message))
    log_warning(paste("错误详情:", toString(e)))
    return(c())
  })
}

# 检查文件稳定性的函数（优化日志）
check_file_stability <- function(file_path, stability_seconds) {
  tryCatch({
    if (!file.exists(file_path)) {
      return(FALSE)
    }

    current_tracker <- file_stability_tracker()
    current_time <- Sys.time()
    file_name <- basename(file_path)

    # 获取当前文件信息
    file_info <- file.info(file_path)
    current_size <- file_info$size
    current_mtime <- file_info$mtime

    # 检查是否已在跟踪
    if (file_path %in% names(current_tracker)) {
      tracker_info <- current_tracker[[file_path]]

      # 检查文件是否发生变化
      if (tracker_info$size == current_size && tracker_info$mtime == current_mtime) {
        # 文件未变化，检查是否已稳定足够时间
        time_diff <- as.numeric(difftime(current_time, tracker_info$first_seen, units = "secs"))

        if (time_diff >= stability_seconds) {
          # 文件稳定，记录成功日志并清理跟踪
          log_info(paste("文件稳定性检查通过:", file_name, "稳定时间:", round(time_diff), "秒"))

          # 从跟踪器中移除已稳定的文件
          current_tracker[[file_path]] <- NULL
          file_stability_tracker(current_tracker)

          return(TRUE)
        } else {
          # 文件仍在稳定期，使用节流日志避免重复消息
          stability_key <- paste("stability_waiting", file_name)
          log_throttled("debug",
                       paste("文件仍在稳定期:", file_name, "已等待:", round(time_diff), "秒，需要:", stability_seconds, "秒"),
                       stability_key, 120)  # 2分钟节流
          return(FALSE)
        }
      } else {
        # 文件发生变化，重新开始跟踪
        log_info(paste("文件发生变化，重新跟踪:", file_name))
        current_tracker[[file_path]] <- list(
          size = current_size,
          mtime = current_mtime,
          first_seen = current_time
        )
        file_stability_tracker(current_tracker)
        return(FALSE)
      }
    } else {
      # 新文件，开始跟踪
      log_info(paste("开始跟踪新文件稳定性:", file_name))
      current_tracker[[file_path]] <- list(
        size = current_size,
        mtime = current_mtime,
        first_seen = current_time
      )
      file_stability_tracker(current_tracker)
      return(FALSE)
    }

  }, error = function(e) {
    log_warning(paste("检查文件稳定性失败:", e$message))
    return(FALSE)
  })
}

# 基于转换状态自动转换文件的函数（带安全措施）
auto_convert_files_by_status <- function(files_needing_conversion, enable_database_conversion = TRUE) {
  if (length(files_needing_conversion) == 0) {
    log_throttled("debug", "自动转换: 没有文件需要处理", "no_files_to_convert", 300)
    return()
  }

  # 安全检查：确保不与手动转换冲突
  if (auto_conversion_in_progress()) {
    log_throttled("warning", "自动转换: 已有转换在进行中，跳过", "conversion_in_progress", 60)
    return()
  }

  # 防止重复执行：检查最近是否刚执行过
  current_time <- Sys.time()
  if (exists("last_auto_conversion_time", envir = last_conversion_check_summary_env)) {
    last_conversion_time <- get("last_auto_conversion_time", envir = last_conversion_check_summary_env)
    time_since_last <- as.numeric(difftime(current_time, last_conversion_time, units = "secs"))

    if (time_since_last < 60) {  # 1分钟内不重复执行
      log_throttled("debug", paste("跳过自动转换：距上次转换仅", round(time_since_last), "秒"),
                   "skip_conversion_too_soon", 60)
      return()
    }
  }

  tryCatch({
    # 更新最后转换时间
    assign("last_auto_conversion_time", current_time, envir = last_conversion_check_summary_env)

    log_throttled("info", "=== 开始自动转换流程 ===", "start_auto_conversion", 300)
    log_info(paste("需要转换的文件数:", length(files_needing_conversion)))

    auto_conversion_in_progress(TRUE)
    session$sendCustomMessage("updateAutoConversionStatus", list(status = "处理中"))

    project_root <- get_project_root_path()
    if (is.null(project_root) || project_root == "") {
      stop("无法获取项目路径")
    }

    # 过滤出稳定的文件（使用优化日志）
    stable_files_to_convert <- c()
    stability_seconds <- file_stability_seconds()

    # 使用节流日志记录稳定性检查参数
    log_throttled("info", paste("文件稳定性检查时间:", stability_seconds, "秒"), "stability_check_time", 600)

    # 记录待检查文件总数
    if (length(files_needing_conversion) > 0) {
      log_info(paste("检查", length(files_needing_conversion), "个文件的稳定性"))
    }

    for (file_path in files_needing_conversion) {
      file_name <- basename(file_path)

      # 安全检查：确保文件仍然存在
      if (!file.exists(file_path)) {
        log_warning(paste("文件不存在，跳过:", file_name))
        next
      }

      # 检查文件稳定性（内部已优化日志）
      if (check_file_stability(file_path, stability_seconds)) {
        stable_files_to_convert <- c(stable_files_to_convert, file_path)
      }
    }

    # 汇总日志
    if (length(stable_files_to_convert) > 0) {
      log_info(paste("共有", length(stable_files_to_convert), "个文件稳定，准备转换"))
    } else if (length(files_needing_conversion) > 0) {
      log_info("所有文件尚未稳定，等待下次检查")
    }

    # 执行转换（如果有稳定的文件需要转换）
    if (length(stable_files_to_convert) > 0) {
      log_info(paste("执行自动转换，文件数:", length(stable_files_to_convert)))

      # 安全检查：确保转换函数存在
      if (!exists("convert_files_with_msconvert")) {
        stop("转换函数不存在")
      }

      # 使用现有的转换函数，但不显示进度条（避免干扰用户）
      convert_result <- convert_files_with_msconvert(
        raw_files = stable_files_to_convert,
        project_root = project_root,
        session = NULL,      # 不显示进度条
        progress_data = NULL,
        enable_database_conversion = enable_database_conversion
      )

      log_info(paste("自动转换完成，成功:", length(stable_files_to_convert), "个文件"))



      if (is_shiny_env()) {
        showNotification(
          paste("自动转换完成，处理了", length(stable_files_to_convert), "个文件"),
          type = "message",
          duration = 5
        )
      }
    } else {
      log_throttled("info", "没有稳定的文件需要自动转换", "no_stable_files", 300)
    }

    log_throttled("info", "=== 自动转换流程结束 ===", "end_auto_conversion", 300)

  }, error = function(e) {
    log_error(paste("自动转换失败:", e$message))
    log_error(paste("错误详情:", toString(e)))
    if (is_shiny_env()) {
      showNotification(paste("自动转换失败:", e$message), type = "error", duration = 5)
    }
  }, finally = {
    # 确保状态正确恢复
    auto_conversion_in_progress(FALSE)
    if (auto_conversion_enabled()) {
      session$sendCustomMessage("updateAutoConversionStatus", list(status = "已启用"))
    } else {
      session$sendCustomMessage("updateAutoConversionStatus", list(status = "已禁用"))
    }
    log_debug("自动转换状态已恢复")
  })
}

# 监控参数标签页切换时自动刷新监控离子列表
observeEvent(input$main_tabs, {
  if (!is.null(input$main_tabs) && input$main_tabs == "监控参数") {
    # 当切换到监控参数标签页时，自动刷新监控离子列表
    tryCatch({
      project_root <- get_project_root_path()
      if (!is.null(project_root) && project_root != "") {
        # 读取监控离子配置文件
        ions_config_path <- file.path(project_root, "data", "monitor_ions_data.yaml")

        if (file.exists(ions_config_path)) {
          ions_config <- yaml::read_yaml(ions_config_path, fileEncoding = "UTF-8")

          if (!is.null(ions_config$monitor_ions_data) && length(ions_config$monitor_ions_data) > 0) {
            ions_list <- ions_config$monitor_ions_data

            new_data <- data.frame(
              ID = character(0),
              化合物名称 = character(0),
              分子质量 = numeric(0),
              保留时间 = numeric(0),
              离子化模式 = character(0),
              离子质荷比 = numeric(0),
              离子类型 = character(0),
              MS级别 = character(0),
              备注 = character(0),
              stringsAsFactors = FALSE
            )

            for (i in seq_along(ions_list)) {
              ion <- ions_list[[i]]
              new_row <- data.frame(
                ID = ion$ID %||% paste0("ion_", i, "_", as.numeric(Sys.time())),
                化合物名称 = ion$化合物名称 %||% "",
                分子质量 = as.numeric(ion$分子质量 %||% 0),
                保留时间 = as.numeric(ion$保留时间 %||% 0),
                离子化模式 = ion$离子化模式 %||% "",
                离子质荷比 = as.numeric(ion$离子质荷比 %||% 0),
                离子类型 = ion$离子类型 %||% "",
                MS级别 = ion$MS级别 %||% "MS1",
                备注 = ion$备注 %||% "",
                stringsAsFactors = FALSE
              )
              new_data <- rbind(new_data, new_row)
            }

            monitor_ions_data(new_data)
            log_info(paste("切换到监控参数标签页，自动加载了", nrow(new_data), "个监控离子配置"))

            if (is_shiny_env()) {
              showNotification(
                paste("已自动加载", nrow(new_data), "个监控离子配置"),
                type = "message",
                duration = 3
              )
            }
          } else {
            # 配置文件存在但为空
            monitor_ions_data(data.frame(
              ID = character(0),
              化合物名称 = character(0),
              分子质量 = numeric(0),
              保留时间 = numeric(0),
              离子化模式 = character(0),
              离子质荷比 = numeric(0),
              离子类型 = character(0),
              MS级别 = character(0),
              备注 = character(0),
              stringsAsFactors = FALSE
            ))
            log_info("监控参数标签页：监控离子配置文件为空")
          }
        } else {
          # 配置文件不存在
          monitor_ions_data(data.frame(
            ID = character(0),
            化合物名称 = character(0),
            分子质量 = numeric(0),
            保留时间 = numeric(0),
            离子化模式 = character(0),
            离子质荷比 = numeric(0),
            离子类型 = character(0),
            MS级别 = character(0),
            备注 = character(0),
            stringsAsFactors = FALSE
          ))
          log_info("监控参数标签页：未找到监控离子配置文件")
        }
      }
    }, error = function(e) {
      log_error(paste("监控参数标签页自动加载监控离子失败:", e$message))
    })
  }
})

# 转换进度状态
conversion_in_progress <- reactiveVal(FALSE)
conversion_progress_data <- reactiveVal(list(completed = 0, total = 0))

# 转换进度显示
output$conversion_in_progress <- reactive({
  conversion_in_progress()
})
outputOptions(output, "conversion_in_progress", suspendWhenHidden = FALSE)

output$conversion_progress_text <- renderText({
  progress <- conversion_progress_data()
  if (progress$total > 0) {
    paste0(progress$completed, "/", progress$total)
  } else {
    "0/0"
  }
})

# 数据转换控制
observeEvent(input$start_conversion, {
  tryCatch({
    log_info("开始手动数据转换流程")

    # 安全检查：防止与自动转换冲突
    if (auto_conversion_in_progress()) {
      log_warning("手动转换: 自动转换正在进行中，请稍后再试")
      if (is_shiny_env()) {
        showNotification("自动转换正在进行中，请稍后再试", type = "warning", duration = 3)
      }
      return()
    }

    # 检查是否有活动项目
    project_root <- get_project_root_path()
    log_info(paste("获取项目路径:", ifelse(is.null(project_root), "NULL", project_root)))

    if (is.null(project_root) || project_root == "") {
      error_msg <- "请先创建或导入项目"
      log_error(error_msg)
      if (is_shiny_env()) {
        showNotification(error_msg, type = "error")
      }
      return()
    }

    data_path <- file.path(project_root, "data")
    if (exists("log_info")) {
      log_info(paste("数据目录路径:", data_path))
    }

    # 从数据索引中获取需要转换的文件
    if (exists("log_info")) {
      log_info("开始加载数据索引")
    }
    index <- load_data_index()
    if (exists("log_info")) {
      log_info(paste("数据索引加载完成，文件数:", length(index$data_files)))
    }

    raw_files <- c()
    # 注意：强制重新转换只影响手动转换，不影响自动转换
    force_reconvert <- input$force_reconvert %||% FALSE

    log_info(paste("手动转换 - 强制重新转换设置:", force_reconvert, "（仅影响手动转换）"))

    if (length(index$data_files) > 0) {
      log_info(paste("开始扫描RAW文件，总文件数:", length(index$data_files)))

      for (i in seq_along(index$data_files)) {
        file_record <- index$data_files[[i]]
        if (is.null(file_record) || is.null(file_record$file_path)) {
          log_warning(paste("文件记录", i, "无效，跳过"))
          next
        }

        file_path <- file_record$file_path
        file_name <- basename(file_path)
        log_debug(paste("检查文件:", file_name))

        # 只处理RAW格式文件
        if (grepl("\\.(raw|d)$", file_path, ignore.case = TRUE)) {
          if (file.exists(file_path)) {
            # 检查是否需要转换
            needs_conversion <- force_reconvert

            if (!force_reconvert) {
              # 检查基于数据索引ID的缓存文件是否存在且有效
              cache_dir_v2 <- file.path(project_root, "data", "cache", "spectra_v2")

              # 使用数据索引ID生成缓存文件名
              project_name <- basename(project_root)
              cache_name <- generate_unique_cache_name(file_path, project_name)
              cache_file <- file.path(cache_dir_v2, paste0(cache_name, ".rds"))

              log_debug(paste("转换检查 - 缓存文件路径:", cache_file))

              if (file.exists(cache_file)) {
                log_debug(paste("转换检查 - 缓存文件存在:", cache_file))

                # 检查缓存文件是否比原文件新且有效
                cache_info <- file.info(cache_file)
                original_info <- file.info(file_path)

                log_debug(paste("转换检查 - 缓存时间:", cache_info$mtime, "原文件时间:", original_info$mtime))
                log_debug(paste("转换检查 - 缓存文件大小:", cache_info$size))

                if (!is.na(cache_info$mtime) && !is.na(original_info$mtime)) {
                  is_newer <- cache_info$mtime > original_info$mtime
                  is_valid_size <- cache_info$size > 1000  # 确保文件有合理大小

                  if (is_newer && is_valid_size) {
                    # 缓存文件有效，但如果启用了数据库转换，还需要检查数据库中是否存在
                    enable_db_conversion <- input$enable_database_conversion %||% TRUE

                    if (enable_db_conversion) {
                      # 检查文件是否已存在于数据库中
                      file_exists_in_db <- check_file_exists_in_database(cache_file)

                      if (file_exists_in_db) {
                        log_info(paste("文件已转换且在数据库中，完全跳过:", file_name))
                        needs_conversion <- FALSE
                      } else {
                        log_info(paste("文件已转换但不在数据库中，需要处理以入库:", file_name))
                        needs_conversion <- TRUE
                      }
                    } else {
                      log_info(paste("文件已转换且缓存有效，跳过:", file_name))
                      needs_conversion <- FALSE
                    }
                  } else {
                    log_info(paste("缓存无效，需要重新转换:", file_name, "- 时间:", is_newer, "大小:", is_valid_size))
                    needs_conversion <- TRUE
                  }
                } else {
                  log_warning(paste("无法获取文件时间信息，需要转换:", file_name))
                  needs_conversion <- TRUE
                }
              } else {
                log_info(paste("缓存文件不存在，需要转换:", file_name))
                needs_conversion <- TRUE
              }
            } else {
              log_info(paste("强制重新转换模式，添加文件:", file_name))
            }

            if (needs_conversion) {
              raw_files <- c(raw_files, file_path)
              log_info(paste("添加待转换RAW文件:", file_name))
            }
          } else {
            log_warning(paste("RAW文件不存在:", file_path))
          }
        } else {
          log_debug(paste("跳过非RAW文件:", file_name))
        }
      }
    } else {
      log_warning("数据索引为空")
    }

    log_info(paste("扫描完成，找到RAW文件数:", length(raw_files)))

    if (length(raw_files) == 0) {
      error_msg <- "未找到需要转换的原始数据文件"
      log_warning(error_msg)
      if (is_shiny_env()) {
        showNotification(error_msg, type = "warning")
      }
      return()
    }

    # 初始化转换状态
    total_files <- length(raw_files)

    # 设置进度状态
    conversion_in_progress(TRUE)
    conversion_progress_data(list(completed = 0, total = total_files))

    # 开始转换过程
    if (is_shiny_env()) {
      showNotification(paste("开始转换", total_files, "个文件"), type = "message")
    }
    log_info(paste("开始转换", total_files, "个文件"))

    # 调用实际的转换函数
    enable_db_conversion <- input$enable_database_conversion %||% TRUE
    convert_files_with_msconvert(raw_files, project_root, session, conversion_progress_data, enable_db_conversion)

    # 转换完成后重置状态并刷新统计
    conversion_in_progress(FALSE)

  }, error = function(e) {
    log_error(paste("开始数据转换失败:", e$message))
    conversion_in_progress(FALSE)  # 出错时也要重置状态

    if (is_shiny_env()) {
      showNotification(paste("转换失败:", e$message), type = "error")
    }
  })
})

# 实际的msconvert转换函数（顺序处理和进度跟踪）
convert_files_with_msconvert <- function(raw_files, project_root, session = NULL, progress_data = NULL, enable_database_conversion = TRUE) {
  tryCatch({
    # 参数验证
    if (missing(raw_files) || is.null(raw_files) || length(raw_files) == 0) {
      stop("raw_files参数为空或长度为零")
    }

    if (missing(project_root) || is.null(project_root) || project_root == "") {
      stop("project_root参数为空")
    }

    log_info(paste("转换函数参数验证通过 - 文件数:", length(raw_files), "项目路径:", project_root))

    # 使用路径管理系统获取msconvert工具路径
    app_root <- get_app_root_path()
    if (is.null(app_root) || app_root == "") {
      stop("无法获取应用根路径")
    }

    msconvert_path <- file.path(app_root, "ProteoWizard", "msconvert.exe")
    log_info(paste("检查msconvert工具路径:", msconvert_path))

    if (!file.exists(msconvert_path)) {
      error_msg <- paste("msconvert工具不存在，请确保ProteoWizard已正确安装\n工具路径位于：", msconvert_path)
      stop(error_msg)
    }

    # 创建输出目录
    output_dir <- file.path(project_root, "data", "mzML")
    if (!dir.exists(output_dir)) {
      dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)
    }

    total_files <- length(raw_files)
    successful_conversions <- 0
    failed_conversions <- 0
    completed_files <- 0

    # 初始化进度跟踪
    if (!is.null(session)) {
      session$sendCustomMessage("updateUnifiedProgressBar", list(
        percentage = 0
      ))
      session$sendCustomMessage("updateConversionStatus", list(
        status = paste0("开始转换 (0/", total_files, ")")
      ))
    }

    # 顺序处理文件
    for (i in seq_along(raw_files)) {
      raw_file <- raw_files[i]
      file_name <- basename(raw_file)

      log_info(paste("开始转换文件", i, "/", total_files, ":", file_name))

      # 执行转换
      result <- process_single_file(raw_file, output_dir, msconvert_path, project_root, enable_database_conversion)

      if (result$success) {
        successful_conversions <- successful_conversions + 1
      } else {
        failed_conversions <- failed_conversions + 1
      }

      completed_files <- completed_files + 1

      # 更新进度
      if (!is.null(progress_data)) {
        progress_data(list(completed = completed_files, total = total_files))
      }
      if (!is.null(session)) {
        progress_percentage <- round((completed_files / total_files) * 100)
        session$sendCustomMessage("updateUnifiedProgressBar", list(
          percentage = progress_percentage
        ))

        # 同时更新转换状态文本
        session$sendCustomMessage("updateConversionStatus", list(
          status = paste0("转换进行中 (", completed_files, "/", total_files, ")")
        ))
      }


    }

    # 转换完成通知
    completion_message <- paste("转换完成！成功:", successful_conversions, "个，失败:", failed_conversions, "个")
    log_info(completion_message)

    # 重置进度状态
    if (!is.null(progress_data)) {
      progress_data(list(completed = 0, total = 0))
    }
    if (!is.null(session)) {
      session$sendCustomMessage("resetUnifiedProgress", list())
      # 转换完成后更新状态
      session$sendCustomMessage("updateConversionStatus", list(
        status = completion_message
      ))
    }



    if (is_shiny_env()) {
      if (failed_conversions == 0) {
        showNotification(completion_message, type = "message")
      } else {
        showNotification(completion_message, type = "warning")
      }
    }

  }, error = function(e) {
    error_msg <- paste("批量转换失败:", e$message)
    log_error(error_msg)

    # 重置进度状态
    if (!is.null(progress_data)) {
      progress_data(list(completed = 0, total = 0))
    }
    if (!is.null(session)) {
      session$sendCustomMessage("resetUnifiedProgress", list())
      # 错误时更新状态
      session$sendCustomMessage("updateConversionStatus", list(
        status = "转换失败"
      ))
    }



    if (is_shiny_env()) {
      showNotification(error_msg, type = "error")
    }
  })
}

# 处理单个文件的转换和读取
process_single_file <- function(raw_file, output_dir, msconvert_path, project_root, enable_database_conversion = TRUE) {
  tryCatch({
    file_name <- basename(raw_file)

    # 检查是否已存在对应的RDS文件
    project_name <- basename(project_root)
    cache_name <- generate_unique_cache_name(raw_file, project_name)
    cache_dir <- file.path(project_root, "data", "cache", "spectra_v2")
    cache_file <- file.path(cache_dir, paste0(cache_name, "_spectra.rds"))

    # 如果RDS文件已存在且启用数据库转换，检查是否需要数据库处理
    if (file.exists(cache_file) && enable_database_conversion) {
      log_info(paste("发现已存在的RDS文件:", basename(cache_file)))

      # 检查文件是否已在数据库中
      file_exists_in_db <- check_file_exists_in_database(cache_file)

      if (file_exists_in_db) {
        log_info(paste("文件已在数据库中，跳过所有处理:", file_name))
        return(list(success = TRUE, message = "文件已存在，跳过处理", skipped = TRUE))
      } else {
        log_info(paste("RDS文件存在但不在数据库中，直接进行数据库插入:", file_name))

        # 直接进行数据库插入，不需要重新转换
        database_result <- store_single_file_to_database(cache_file)

        if (database_result$success) {
          log_info(paste("已存在RDS文件成功插入数据库:", file_name))
          return(list(success = TRUE, message = "RDS文件已插入数据库", database_inserted = TRUE))
        } else {
          log_warning(paste("已存在RDS文件数据库插入失败:", database_result$error))
          return(list(success = FALSE, error = paste("数据库插入失败:", database_result$error)))
        }
      }
    }

    # 执行msconvert转换（当RDS不存在或不需要数据库转换时）
    conversion_result <- convert_single_file_msconvert(raw_file, output_dir, msconvert_path)

    if (conversion_result$success) {
      log_info(paste("文件转换成功:", file_name))

      # 转换成功后立即读取mzML文件
      log_info(paste("开始读取转换后的mzML文件:", file_name))
      read_result <- read_and_save_mzml(conversion_result$output_file, project_root, enable_database_conversion)

      if (read_result$success) {
        log_info(paste("mzML文件读取成功，包含", read_result$spectra_length, "个谱图"))
        return(list(success = TRUE, message = "转换和读取成功"))
      } else {
        log_warning(paste("mzML文件读取失败:", read_result$error))
        return(list(success = FALSE, error = paste("读取失败:", read_result$error)))
      }
    } else {
      log_error(paste("文件转换失败:", file_name, "-", conversion_result$error))
      return(list(success = FALSE, error = conversion_result$error))
    }

  }, error = function(e) {
    return(list(success = FALSE, error = e$message))
  })
}

# 单个文件转换函数
convert_single_file_msconvert <- function(input_file, output_dir, msconvert_path) {
  tryCatch({
    # 构建msconvert命令
    # 基于您提供的命令模板
    cmd <- paste0(
      '"', msconvert_path, '"',
      ' --zlib',
      ' --filter "peakPicking vendor msLevel=1-"',
      ' --filter "titleMaker <RunId>.<ScanNumber>.<ScanNumber>.<ChargeState> File:\\"^<SourcePath^>\\", NativeID:\\"^<Id^>\\""',
      ' "', input_file, '"',
      ' -o "', output_dir, '"'
    )

    log_info(paste("执行命令:", cmd))

    # 执行转换命令
    result <- system(cmd, intern = TRUE, show.output.on.console = FALSE)
    exit_code <- attr(result, "status")

    # 检查转换结果
    base_name <- tools::file_path_sans_ext(basename(input_file))
    output_file <- file.path(output_dir, paste0(base_name, ".mzML"))

    if (is.null(exit_code) || exit_code == 0) {
      if (file.exists(output_file)) {
        return(list(
          success = TRUE,
          output_file = output_file,
          message = "转换成功"
        ))
      } else {
        return(list(
          success = FALSE,
          error = "转换命令执行成功但未找到输出文件"
        ))
      }
    } else {
      return(list(
        success = FALSE,
        error = paste("msconvert执行失败，退出码:", exit_code)
      ))
    }

  }, error = function(e) {
    return(list(
      success = FALSE,
      error = paste("转换过程出错:", e$message)
    ))
  })
}

# 检查RDS文件是否已存在于数据库中
check_file_exists_in_database <- function(rds_file) {
  tryCatch({
    # 从RDS文件名提取数据索引ID
    file_name <- basename(rds_file)
    rds_base_name <- gsub("_spectra\\.rds$", "", file_name)

    # 检查是否为标准的数据索引ID格式
    if (!grepl("^file_\\d{8}_\\d{6}_\\d+$", rds_base_name)) {
      log_warning(paste("RDS文件名格式不符合数据索引ID标准:", file_name))
      return(FALSE)
    }

    data_index_id <- rds_base_name

    # 获取数据库路径
    current_project_root <- get_project_root_path()
    if (is.null(current_project_root)) {
      log_warning("无法获取项目根路径，无法检查数据库")
      return(FALSE)
    }

    db_path <- get_project_absolute_path("results", "spectra.db")

    # 如果数据库文件不存在，说明文件肯定不在数据库中
    if (!file.exists(db_path)) {
      return(FALSE)
    }

    # 连接数据库并检查文件是否存在
    con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
    tryCatch({
      # 检查data_files表中是否存在该文件记录
      query <- "SELECT COUNT(*) as count FROM data_files WHERE file_id = ? OR data_index_id = ?"
      result <- DBI::dbGetQuery(con, query, list(data_index_id, data_index_id))

      exists_in_db <- result$count > 0

      if (exists_in_db) {
        log_info(paste("文件已存在于数据库中:", data_index_id))
      } else {
        log_info(paste("文件不存在于数据库中:", data_index_id))
      }

      return(exists_in_db)

    }, finally = {
      DBI::dbDisconnect(con)
    })

  }, error = function(e) {
    log_warning(paste("检查数据库文件存在性失败:", e$message))
    return(FALSE)  # 出错时假设文件不存在，允许重新插入
  })
}

# 将单个文件的数据存储到数据库
store_single_file_to_database <- function(rds_file) {
  tryCatch({
    # 检查数据库转换模块是否已加载
    if (!exists("process_single_file_concurrent_safe") || !exists("create_database_schema")) {
      log_warning("数据库转换模块未加载，跳过数据库存储")
      return(list(success = FALSE, error = "数据库转换模块未加载"))
    }

    # 使用路径管理服务获取数据库路径
    current_project_root <- get_project_root_path()
    if (is.null(current_project_root)) {
      log_error("无法获取项目根路径")
      return(list(success = FALSE, error = "无法获取项目根路径"))
    }

    # 构建数据库文件路径
    db_path <- get_project_absolute_path("results", "spectra.db")

    # 确保results目录存在
    results_dir <- get_project_absolute_path("results")
    if (!validate_path(results_dir, create_if_missing = TRUE)) {
      log_error("无法创建结果目录:", results_dir)
      return(list(success = FALSE, error = "无法创建结果目录"))
    }

    # 如果数据库不存在，创建数据库结构
    if (!file.exists(db_path)) {
      log_info(paste0("创建新的数据库文件:", db_path))
      con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
      tryCatch({
        create_database_schema(con)
      }, finally = {
        DBI::dbDisconnect(con)
      })
    }

    # 使用并发安全的处理函数
    result <- process_single_file_concurrent_safe(rds_file, db_path)

    if (result$success) {
      log_info(paste("文件数据成功存储到数据库:", basename(rds_file)))
    } else {
      log_error(paste("数据库存储失败:", result$error))
    }

    return(result)

  }, error = function(e) {
    log_error(paste("数据库存储失败:", e$message))
    return(list(success = FALSE, error = e$message))
  })
}

# mzML文件读取和保存函数
read_and_save_mzml <- function(mzml_file, project_root, enable_database_conversion = TRUE) {
  tryCatch({
    log_info(paste("开始读取mzML文件:", basename(mzml_file)))

    # 使用MsExperiment包读取mzML文件
    if (!requireNamespace("MsExperiment", quietly = TRUE)) {
      stop("MsExperiment包未安装，请先安装: BiocManager::install('MsExperiment')")
    }

    # 读取mzML文件为Spectra对象
    spectra_obj <- Spectra::Spectra(mzml_file, source = Spectra::MsBackendMzR(),backend =Spectra::MsBackendMemory())

    if (length(spectra_obj) == 0) {
      stop("读取的Spectra对象为空")
    }

    log_info(paste("成功读取Spectra对象，包含", length(spectra_obj), "个谱图"))

    # 创建缓存目录
    cache_dir <- file.path(project_root, "data", "cache", "spectra_v2")
    if (!dir.exists(cache_dir)) {
      dir.create(cache_dir, recursive = TRUE, showWarnings = FALSE)
    }


    # 生成基于数据索引ID的缓存文件名
    # 尝试从数据索引中查找对应的RAW文件
    mzml_base <- tools::file_path_sans_ext(basename(mzml_file))
    project_name <- basename(project_root)

    # 首先尝试通过文件名在数据索引中查找
    original_raw_file <- NULL

    # 加载数据索引
    if (exists("load_data_index")) {
      tryCatch({
        index <- load_data_index(project_name)
        if (!is.null(index) && length(index$data_files) > 0) {
          # 查找文件名匹配的记录
          for (file_id in names(index$data_files)) {
            file_record <- index$data_files[[file_id]]
            if (!is.null(file_record$file_name)) {
              # 移除扩展名进行比较
              record_base <- tools::file_path_sans_ext(file_record$file_name)
              if (record_base == mzml_base) {
                original_raw_file <- file_record$file_path
                break
              }
            }
          }
        }
      }, error = function(e) {
        log_warning(paste("查找数据索引失败:", e$message))
      })
    }

    if (!is.null(original_raw_file)) {
      # 使用数据索引ID生成文件名
      cache_name <- generate_unique_cache_name(original_raw_file, project_name)
      cache_file <- get_rds_file_path_by_id(gsub("_spectra$", "", cache_name), cache_dir)
    } else {
      # 回退到基于文件名的方法
      log_warning(paste("未在数据索引中找到对应的RAW文件:", mzml_base))
      base_name <- tools::file_path_sans_ext(basename(mzml_file))
      cache_file <- file.path(cache_dir, paste0(base_name, "_spectra.rds"))
    }

    # 保存Spectra对象为RDS文件
    saveRDS(spectra_obj, cache_file)
    log_info(paste("Spectra对象已保存到:", cache_file))

    # 根据用户设置决定是否进行数据库转换
    database_result <- list(success = FALSE)  # 默认值
    if (enable_database_conversion) {
      # 检查文件是否已存在于数据库中
      file_exists_in_db <- check_file_exists_in_database(cache_file)

      if (file_exists_in_db) {
        log_info(paste("文件已存在于数据库中，跳过数据库存储:", basename(cache_file)))
        database_result <- list(success = TRUE, skipped = TRUE)
      } else {
        # 文件不存在于数据库中，进行数据库存储
        log_info(paste("文件不存在于数据库中，开始数据库存储:", basename(cache_file)))
        database_result <- store_single_file_to_database(cache_file)

        if (database_result$success) {
          log_info(paste("文件数据已存储到数据库:", basename(cache_file)))

          # 数据库存储成功后，删除mzML文件以节省空间
          if (file.exists(mzml_file)) {
            tryCatch({
              file.remove(mzml_file)
              log_info(paste("已删除mzML文件以节省空间:", basename(mzml_file)))
            }, error = function(e) {
              log_warning(paste("删除mzML文件失败:", e$message))
            })
          }
        } else {
          log_warning(paste("数据库存储失败:", database_result$error))
        }
      }
    } else {
      log_info("数据库转换已禁用，跳过数据库存储")
    }

    # 返回基本信息
    return(list(
      success = TRUE,
      cache_file = cache_file,
      spectra_length = length(spectra_obj),
      mz_range = range(unlist(Spectra::mz(spectra_obj)), na.rm = TRUE),
      rt_range = range(Spectra::rtime(spectra_obj), na.rm = TRUE),
      ms_levels = unique(Spectra::msLevel(spectra_obj)),
      database_stored = database_result$success
    ))

  }, error = function(e) {
    error_msg <- paste("读取mzML文件失败:", e$message)
    log_error(error_msg)
    return(list(
      success = FALSE,
      error = error_msg
    ))
  })
}

# 批量读取和保存mzML文件
batch_read_mzml_files <- function(project_root, enable_database_conversion = TRUE) {
  tryCatch({
    mzml_dir <- file.path(project_root, "data", "mzML")
    if (!dir.exists(mzml_dir)) {
      log_warning("mzML目录不存在")
      return(list())
    }

    # 查找所有mzML文件
    mzml_files <- list.files(mzml_dir, pattern = "\\.mzML$",
                            ignore.case = TRUE, full.names = TRUE)

    if (length(mzml_files) == 0) {
      log_info("未找到mzML文件")
      return(list())
    }

    log_info(paste("找到", length(mzml_files), "个mzML文件，开始批量读取"))

    results <- list()
    for (mzml_file in mzml_files) {
      file_name <- basename(mzml_file)
      log_info(paste("处理文件:", file_name))

      result <- read_and_save_mzml(mzml_file, project_root, enable_database_conversion)
      results[[file_name]] <- result
    }

    return(results)

  }, error = function(e) {
    log_error(paste("批量读取mzML文件失败:", e$message))
    return(list())
  })
}

# 模拟转换进度（简化版）
simulate_conversion_progress <- function(files) {
  total_files <- length(files)

  for (i in seq_along(files)) {
    file_name <- basename(files[i])
    log_info(paste("模拟转换文件", i, "/", total_files, ":", file_name))

    # 模拟转换时间
    Sys.sleep(2)
  }

  showNotification("所有文件转换完成", type = "message")
}



# 导入已配置离子按钮
observeEvent(input$import_configured_ions, {
  tryCatch({
    project_root <- get_project_root_path()
    if (is.null(project_root)) {
      if (is_shiny_env()) {
        showNotification("请先创建或导入项目", type = "error")
      } else {
        log_error("请先创建或导入项目")
      }
      return()
    }

    ions_config_path <- file.path(project_root, "data", "monitor_ions_data.yaml")
    if (!file.exists(ions_config_path)) {
      if (is_shiny_env()) {
        showNotification("未找到监控离子配置文件", type = "warning")
      } else {
        log_warning("未找到监控离子配置文件")
      }
      return()
    }

    ions_config <- yaml::read_yaml(ions_config_path)
    if (is.null(ions_config$monitor_ions_data) || length(ions_config$monitor_ions_data) == 0) {
      if (is_shiny_env()) {
        showNotification("监控离子配置文件为空", type = "warning")
      } else {
        log_warning("监控离子配置文件为空")
      }
      return()
    }

    # 提取离子m/z值
    ion_mz_values <- sapply(ions_config$monitor_ions_data, function(x) x$离子质荷比)
    ion_mz_string <- paste(ion_mz_values, collapse = ", ")

    # 更新输入框
    if (is_shiny_env()) {
      updateTextAreaInput(session, "monitor_ions_input", value = ion_mz_string)
      showNotification(paste("成功导入", length(ion_mz_values), "个监控离子"), type = "message")
    }

    log_info(paste("手动导入监控离子:", length(ion_mz_values), "个"))

  }, error = function(e) {
    log_error(paste("导入监控离子失败:", e$message))
    if (is_shiny_env()) {
      showNotification(paste("导入失败:", e$message), type = "error")
    }
  })
})

# 解析监控离子输入
parse_monitor_ions <- function(ions_input) {
  if (is.null(ions_input) || ions_input == "") {
    return(NULL)
  }

  tryCatch({
    # 支持逗号分隔的数值
    ions_str <- gsub("\\s+", "", ions_input)  # 移除空格
    ions_vec <- as.numeric(strsplit(ions_str, ",")[[1]])
    ions_vec <- ions_vec[!is.na(ions_vec)]  # 移除无效值

    if (length(ions_vec) > 0) {
      return(ions_vec)
    } else {
      return(NULL)
    }
  }, error = function(e) {
    log_warning(paste("解析监控离子失败:", e$message))
    return(NULL)
  })
}

# 监控相关功能已移除，专注于数据管理和监控参数配置

# 项目导入按钮（假设为 input$import_project_btn）
observeEvent(input$import_project_btn, {
  tryCatch({
    # 这里假设已有项目导入逻辑
    # ...项目导入相关代码...

    # 导入成功后，强制刷新监控离子列表
    project_root <- get_project_root_path()
    if (!is.null(project_root) && project_root != "") {
      ions_config_path <- file.path(project_root, "data", "monitor_ions_data.yaml")
      if (file.exists(ions_config_path)) {
        ions_config <- yaml::read_yaml(ions_config_path, fileEncoding = "UTF-8")
        if (!is.null(ions_config$monitor_ions_data) && length(ions_config$monitor_ions_data) > 0) {
          ions_list <- ions_config$monitor_ions_data
          new_data <- data.frame(
            ID = character(0),
            化合物名称 = character(0),
            分子质量 = numeric(0),
            保留时间 = numeric(0),
            扫描模式 = character(0),
            离子类型 = character(0),
            离子质荷比 = numeric(0),
            stringsAsFactors = FALSE
          )
          for (i in seq_along(ions_list)) {
            ion <- ions_list[[i]]
            new_row <- data.frame(
              ID = ion$ID %||% paste0("ion_", i, "_", as.numeric(Sys.time())),
              化合物名称 = ion$化合物名称 %||% "",
              分子质量 = as.numeric(ion$分子质量 %||% 0),
              保留时间 = as.numeric(ion$保留时间 %||% 0),
              扫描模式 = ion$扫描模式 %||% "",
              离子类型 = ion$离子类型 %||% "",
              离子质荷比 = as.numeric(ion$离子质荷比 %||% 0),
              stringsAsFactors = FALSE
            )
            new_data <- rbind(new_data, new_row)
          }
          monitor_ions_data(new_data)
          if (is_shiny_env()) {
            showNotification(paste("已自动加载", nrow(new_data), "个监控离子配置"), type = "message", duration = 3)
          }
        } else {
          monitor_ions_data(data.frame(
            ID = character(0),
            化合物名称 = character(0),
            分子质量 = numeric(0),
            保留时间 = numeric(0),
            扫描模式 = character(0),
            离子类型 = character(0),
            离子质荷比 = numeric(0),
            stringsAsFactors = FALSE
          ))
        }
      } else {
        monitor_ions_data(data.frame(
          ID = character(0),
          化合物名称 = character(0),
          分子质量 = numeric(0),
          保留时间 = numeric(0),
          扫描模式 = character(0),
          离子类型 = character(0),
          离子质荷比 = numeric(0),
          stringsAsFactors = FALSE
        ))
      }
    }
  }, error = function(e) {
    log_error(paste("项目导入后刷新监控离子失败:", e$message))
  })
})

# EIC提取功能已移除，将在后续版本中重新实现
# 注意：xcms依赖已移除，相关功能将使用自主开发的算法替代

# 占位符：EIC提取状态
eic_extraction_status <- reactiveVal("功能开发中")

# 占位符：EIC提取结果显示
output$eic_extraction_status <- renderText({
  "EIC提取功能正在重新开发中，敬请期待..."
})

output$eic_extraction_result_table <- DT::renderDataTable({
  # 返回空表格
  data.frame(
    提示 = "EIC提取功能正在重新开发中",
    说明 = "该功能将使用自主开发的算法替代xcms包",
    stringsAsFactors = FALSE
  )
})

# ============================================================================
# MS2-MS1峰匹配功能
# ============================================================================

# 匹配状态响应式值
ms_matching_status <- reactiveVal("未开始")
ms_matching_progress <- reactiveVal(0)
ms_matching_results <- reactiveVal(NULL)

# 输出匹配状态
output$ms_matching_status <- renderText({
  ms_matching_status()
})

# 输出匹配进度
output$ms_matching_progress <- renderText({
  if (ms_matching_progress() > 0) {
    paste0("进度: ", round(ms_matching_progress(), 1), "%")
  } else {
    ""
  }
})

# 匹配结果表格
output$ms_matching_result_table <- DT::renderDataTable({
  results <- ms_matching_results()
  if (is.null(results) || is.null(results$file_statistics)) {
    return(data.frame(
      文件名 = character(0),
      总MS2数 = integer(0),
      已匹配数 = integer(0),
      匹配率 = character(0),
      stringsAsFactors = FALSE
    ))
  }

  stats <- results$file_statistics
  display_data <- data.frame(
    文件名 = basename(stats$file_name),
    总MS2数 = stats$total_ms2,
    已匹配数 = stats$matched_ms2,
    匹配率 = paste0(round(stats$matched_ms2 / stats$total_ms2 * 100, 1), "%"),
    stringsAsFactors = FALSE
  )

  display_data
}, options = list(
  pageLength = 10,
  scrollX = TRUE,
  language = list(url = '//cdn.datatables.net/plug-ins/1.10.11/i18n/Chinese.json')
))

# 开始MS2-MS1匹配
observeEvent(input$start_ms_matching, {
  tryCatch({
    project_root <- get_project_root_path()
    if (is.null(project_root)) {
      showNotification("请先创建或导入项目", type = "warning")
      return()
    }

    # 获取数据库路径
    db_path <- file.path(project_root, "results", "spectra.db")

    if (!file.exists(db_path)) {
      showNotification("数据库文件不存在，请先进行数据转换", type = "error")
      return()
    }

    # 重置状态
    ms_matching_status("准备中...")
    ms_matching_progress(0)
    ms_matching_results(NULL)

    # 获取匹配参数
    ppm_tolerance <- input$ms_matching_ppm_tolerance
    if (is.null(ppm_tolerance) || is.na(ppm_tolerance) || ppm_tolerance <= 0) {
      ppm_tolerance <- 20  # 默认20ppm
    }

    # 立即更新状态，避免页面卡住
    ms_matching_status("正在匹配中...")
    ms_matching_progress(0)

    # 创建一个定时器来模拟进度更新
    start_time <- Sys.time()
    progress_timer <- reactiveTimer(2000)  # 每2秒更新一次

    # 监听定时器，更新进度状态
    observe({
      progress_timer()

      # 只在匹配进行中时更新
      if (ms_matching_status() == "正在匹配中...") {
        elapsed_time <- as.numeric(difftime(Sys.time(), start_time, units = "secs"))
        # 简单的进度估算（基于时间）
        estimated_progress <- min(90, elapsed_time * 2)  # 最多到90%，等待实际完成
        ms_matching_progress(estimated_progress)
        ms_matching_status(paste0("正在匹配中... 预计进度: ", round(estimated_progress, 1), "%"))
      }
    })

    # 在后台执行匹配
    future({
      # 执行匹配（不使用进度回调，避免跨线程问题）
      result <- match_ms2_to_ms1_peaks(
        db_path = db_path,
        ppm_tolerance = ppm_tolerance,
        batch_size = 1000,
        progress_callback = NULL  # 暂时禁用进度回调
      )

      return(result)

    }) %...>% (function(result) {
      if (result$success) {
        ms_matching_status(paste0("匹配完成 - 成功匹配 ", result$matched_count, " 个MS2扫描"))
        ms_matching_progress(100)

        # 获取详细统计信息
        stats <- get_matching_statistics(db_path)
        ms_matching_results(stats)

        showNotification(
          paste0("MS2-MS1匹配完成！匹配率: ", round(result$match_rate * 100, 1), "%"),
          type = "message",
          duration = 5
        )
      } else {
        ms_matching_status(paste0("匹配失败: ", result$error))
        showNotification(paste0("匹配失败: ", result$error), type = "error")
      }
    }) %...!% (function(error) {
      ms_matching_status(paste0("匹配出错: ", error$message))
      showNotification(paste0("匹配出错: ", error$message), type = "error")
    })

  }, error = function(e) {
    log_error(paste("MS2-MS1匹配启动失败:", e$message))
    showNotification(paste0("匹配启动失败: ", e$message), type = "error")
  })
})

# 查看匹配统计
observeEvent(input$view_ms_matching_stats, {
  tryCatch({
    project_root <- get_project_root_path()
    if (is.null(project_root)) {
      showNotification("请先创建或导入项目", type = "warning")
      return()
    }

    db_path <- file.path(project_root, "results", "spectra.db")

    if (!file.exists(db_path)) {
      showNotification("数据库文件不存在", type = "error")
      return()
    }

    stats <- get_matching_statistics(db_path)
    ms_matching_results(stats)

    # 更新状态显示
    if (stats$total_ms2 > 0) {
      ms_matching_status(paste0(
        "统计信息 - 总MS2: ", stats$total_ms2,
        ", 已匹配: ", stats$matched_ms2,
        ", 匹配率: ", round(stats$match_rate * 100, 1), "%"
      ))
    } else {
      ms_matching_status("数据库中没有MS2数据")
    }

  }, error = function(e) {
    log_error(paste("获取匹配统计失败:", e$message))
    showNotification(paste0("获取统计信息失败: ", e$message), type = "error")
  })
})

# ============================================================================
# 未完成模块的占位符实现
# ============================================================================

# 重置匹配结果
observeEvent(input$reset_ms_matching, {
  tryCatch({
    project_root <- get_project_root_path()
    if (is.null(project_root)) {
      showNotification("请先创建或导入项目", type = "warning")
      return()
    }

    # 确认对话框
    showModal(modalDialog(
      title = "确认重置",
      "这将清除所有MS2-MS1匹配结果，此操作不可撤销。确定要继续吗？",
      footer = tagList(
        modalButton("取消"),
        actionButton("confirm_reset_matching", "确认重置", class = "btn-danger")
      )
    ))

  }, error = function(e) {
    log_error(paste("重置匹配对话框失败:", e$message))
    showNotification(paste0("操作失败: ", e$message), type = "error")
  })
})

# 确认重置匹配结果
observeEvent(input$confirm_reset_matching, {
  tryCatch({
    removeModal()

    project_root <- get_project_root_path()
    db_path <- file.path(project_root, "results", "spectra.db")

    if (!file.exists(db_path)) {
      showNotification("数据库文件不存在", type = "error")
      return()
    }

    con <- DBI::dbConnect(RSQLite::SQLite(), db_path)

    # 清除所有匹配结果
    DBI::dbExecute(con, "UPDATE ms2_spectra_data SET ms1_peak_id = NULL WHERE ms1_peak_id IS NOT NULL")

    DBI::dbDisconnect(con)

    # 重置状态
    ms_matching_status("匹配结果已重置")
    ms_matching_progress(0)
    ms_matching_results(NULL)

    showNotification("MS2-MS1匹配结果已重置", type = "message")

  }, error = function(e) {
    if (exists("con") && DBI::dbIsValid(con)) {
      DBI::dbDisconnect(con)
    }
    log_error(paste("重置匹配结果失败:", e$message))
    showNotification(paste0("重置失败: ", e$message), type = "error")
  })
})



# QC监控模块占位符
observeEvent(input$apply_qc_monitoring, {
  showNotification(
    "QC监控功能正在重新开发中，敬请期待...",
    type = "message",
    duration = 5
  )
})

observeEvent(input$run_qc_analysis, {
  showNotification(
    "QC分析功能正在重新开发中，该功能将提供全面的质量控制分析能力",
    type = "message",
    duration = 5
  )
})

# MS2监控模块占位符
observeEvent(input$apply_ms2_monitoring, {
  showNotification(
    "MS2监控功能正在重新开发中，敬请期待...",
    type = "info",
    duration = 5
  )
})

observeEvent(input$run_ms2_analysis, {
  showNotification(
    "MS2分析功能正在重新开发中，该功能将提供深度的二级质谱分析能力",
    type = "info",
    duration = 5
  )
})

# 文件诊断模块占位符
observeEvent(input$run_file_diagnostics, {
  showNotification(
    "文件诊断功能正在重新开发中，敬请期待...",
    type = "info",
    duration = 5
  )
})

observeEvent(input$generate_diagnostics_report, {
  showNotification(
    "诊断报告生成功能正在重新开发中，该功能将提供详细的文件质量诊断报告",
    type = "info",
    duration = 5
  )
})





# 数据库转换功能
# ============================================================================

# 数据库转换状态
database_conversion_status <- reactiveVal("未开始")
database_conversion_in_progress <- reactiveVal(FALSE)

# 数据库转换状态显示
output$database_conversion_status <- renderText({
  database_conversion_status()
})

# 旧的批量数据库转换逻辑已移除
# 现在数据库转换在每个文件转换过程中自动进行，由enable_database_conversion复选框控制

# 辅助函数
# ============================================================================

# 获取需要转换的RAW文件
get_files_to_convert <- function() {
  project_path <- get_project_root_path()
  if (is.null(project_path)) {
    return(character(0))
  }

  tryCatch({
    # 获取数据索引中的所有RAW文件（使用非响应式方法）
    data_list <- NULL

    # 尝试使用响应式函数（如果在响应式上下文中）
    if (exists("get_data_index_dataframe")) {
      tryCatch({
        data_list <- get_data_index_dataframe()
      }, error = function(e) {
        if (grepl("reactive context", e$message)) {
          log_debug("不在响应式上下文中，使用备用方法获取数据索引")
          data_list <- NULL
        } else {
          stop(e)
        }
      })
    }

    # 如果响应式方法失败，使用直接读取索引文件的方法
    if (is.null(data_list) || !is.data.frame(data_list) || nrow(data_list) == 0) {
      log_debug("使用备用方法：直接从索引文件读取")

      # 直接读取数据索引文件
      index <- tryCatch({
        load_data_index()
      }, error = function(e) {
        log_warning(paste("加载数据索引失败:", e$message))
        list(data_files = list())
      })

      if (is.null(index) || !is.list(index) || length(index$data_files) == 0) {
        log_debug("获取RAW文件: 数据索引为空")
        return(character(0))
      }

      # 将索引转换为数据框格式
      data_list <- data.frame(
        文件名 = sapply(index$data_files, function(x) x$file_name),
        路径 = sapply(index$data_files, function(x) x$file_path),
        stringsAsFactors = FALSE
      )
    }

    if (is.null(data_list) || !is.data.frame(data_list) || nrow(data_list) == 0) {
      log_debug("获取RAW文件: 最终数据索引为空")
      return(character(0))
    }

    # 过滤RAW文件
    raw_files <- c()
    for (i in 1:nrow(data_list)) {
      file_path <- data_list$路径[i]
      file_name <- data_list$文件名[i]

      if (!is.na(file_path) && !is.na(file_name) &&
          grepl("\\.raw$", file_name, ignore.case = TRUE)) {
        if (file.exists(file_path)) {
          raw_files <- c(raw_files, file_path)
        }
      }
    }

    # 过滤出需要转换的文件（没有对应mzML文件的）
    output_dir <- file.path(project_path, "data", "mzML")
    files_to_convert <- c()

    for (raw_file in raw_files) {
      base_name <- tools::file_path_sans_ext(basename(raw_file))
      mzml_file <- file.path(output_dir, paste0(base_name, ".mzML"))

      if (!file.exists(mzml_file)) {
        files_to_convert <- c(files_to_convert, raw_file)
      }
    }

    log_debug(paste("找到需要转换的RAW文件数:", length(files_to_convert)))
    return(files_to_convert)

  }, error = function(e) {
    log_error(paste("获取需要转换的文件失败:", e$message))
    return(character(0))
  })
}





# 加载同位素内标EIC相关服务
safe_source("server/isotope_eic_server.R")

# 查看EIC提取结果按钮
observeEvent(input$view_eic_results, {
  results <- isotope_eic_results()
  if (is.null(results)) {
    showNotification("暂无EIC提取结果", type = "warning")
    return()
  }

  # 显示结果摘要
  if (!is.null(results$metadata)) {
    total_compounds <- results$metadata$total_compounds %||% 0
    showNotification(paste("EIC提取结果：共", total_compounds, "个化合物"), type = "message")
  } else {
    showNotification("EIC提取结果已加载", type = "message")
  }
})

} else {
  # 不在Shiny环境中，跳过所有代码
  cat("[DEBUG] workspace_server.R: 不在Shiny环境中，跳过加载\n")
}

