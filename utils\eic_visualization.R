# EIC数据可视化工具
# 用于创建同位素内标EIC数据的3D散点图和MS2碎片信息可视化

# 加载必要的包和脚本
safe_source("utils/logger.R")

# 加载必要的包
load_eic_visualization_packages <- function() {
  required_packages <- c("plotly", "dplyr", "RColorBrewer")
  
  for (pkg in required_packages) {
    if (!requireNamespace(pkg, quietly = TRUE)) {
      if (pkg == "plotly") {
        stop(paste("Required package", pkg, "is not installed. Please install it first: install.packages('plotly')"))
      } else {
        log_warning(paste("Optional package", pkg, "is not installed. Some features may not be available."))
      }
    } else {
      suppressPackageStartupMessages(library(pkg, character.only = TRUE))
    }
  }
}

# EIC可视化器类
EICVisualizer <- setRefClass("EICVisualizer",
  fields = list(
    max_ms2_spectra = "numeric",     # 最大MS2谱图数量（性能优化）
    color_palette = "character",      # 颜色调色板
    point_size_range = "numeric"      # 点大小范围
  ),
  
  methods = list(
    initialize = function(max_ms2_spectra = 50, color_palette = "Set3", point_size_range = c(3, 10)) {
      .self$max_ms2_spectra <- max_ms2_spectra
      .self$color_palette <- color_palette
      .self$point_size_range <- point_size_range
      
      load_eic_visualization_packages()
      log_info("EIC可视化器初始化完成")
    },
    
    # 准备3D散点图数据
    prepare_3d_scatter_data = function(eic_data) {
      if (is.null(eic_data) || is.null(eic_data$compounds)) {
        return(NULL)
      }
      
      plot_data <- data.frame(
        compound_name = character(0),
        sample_name = character(0),
        file_id = character(0),
        rtime = numeric(0),
        intensity = numeric(0),
        mz = numeric(0),
        target_mz = numeric(0),
        mz_error_ppm = numeric(0),
        has_ms2 = logical(0),
        sample_type = character(0),
        stringsAsFactors = FALSE
      )
      
      for (compound_name in names(eic_data$compounds)) {
        compound_data <- eic_data$compounds[[compound_name]]
        target_mz <- compound_data$isotope_info$target_mz
        
        if (!is.null(compound_data$eic_data_by_file)) {
          for (file_id in names(compound_data$eic_data_by_file)) {
            file_data <- compound_data$eic_data_by_file[[file_id]]
            
            if (!is.null(file_data$eic_points) && length(file_data$eic_points) > 0) {
              for (point in file_data$eic_points) {
                # 检查是否有MS2数据
                peak_id <- point$peak_id
                has_ms2 <- !is.null(compound_data$ms2_data_by_peak[[as.character(peak_id)]]) &&
                          length(compound_data$ms2_data_by_peak[[as.character(peak_id)]]) > 0
                
                # 计算mz偏差
                mz_error_ppm <- abs(point$mz - target_mz) / target_mz * 1e6
                
                new_row <- data.frame(
                  compound_name = compound_name,
                  sample_name = file_data$file_info$file_name %||% file_id,
                  file_id = file_id,
                  rtime = point$rtime,
                  intensity = point$intensity,
                  mz = point$mz,
                  target_mz = target_mz,
                  mz_error_ppm = mz_error_ppm,
                  has_ms2 = has_ms2,
                  sample_type = file_data$file_info$sample_type %||% "Unknown",
                  stringsAsFactors = FALSE
                )
                
                plot_data <- rbind(plot_data, new_row)
              }
            }
          }
        }
      }
      
      return(plot_data)
    },
    
    # 创建3D散点图
    create_3d_scatter_plot = function(eic_data, compound_name = NULL) {
      plot_data <- prepare_3d_scatter_data(eic_data)
      
      if (is.null(plot_data) || nrow(plot_data) == 0) {
        return(NULL)
      }
      
      # 如果指定了化合物名称，则只显示该化合物
      if (!is.null(compound_name)) {
        plot_data <- plot_data[plot_data$compound_name == compound_name, ]
        if (nrow(plot_data) == 0) {
          return(NULL)
        }
      }
      
      # 创建颜色映射（基于mz偏差）
      plot_data$color_group <- cut(plot_data$mz_error_ppm, 
                                  breaks = c(0, 5, 10, 20, Inf),
                                  labels = c("优秀(<5ppm)", "良好(5-10ppm)", "一般(10-20ppm)", "较差(>20ppm)"),
                                  include.lowest = TRUE)
      
      # 创建形状映射（基于是否有MS2）
      plot_data$shape <- ifelse(plot_data$has_ms2, "有MS2", "无MS2")
      
      # 创建3D散点图
      p <- plot_ly(
        data = plot_data,
        x = ~rtime,
        y = ~intensity,
        z = ~sample_name,
        color = ~color_group,
        symbol = ~shape,
        type = "scatter3d",
        mode = "markers",
        marker = list(
          size = 5,
          opacity = 0.8
        ),
        text = ~paste(
          "化合物:", compound_name, "<br>",
          "样本:", sample_name, "<br>",
          "保留时间:", round(rtime, 3), "min<br>",
          "强度:", format(intensity, scientific = TRUE), "<br>",
          "实际mz:", round(mz, 4), "<br>",
          "mz偏差:", round(mz_error_ppm, 2), "ppm<br>",
          "MS2数据:", ifelse(has_ms2, "有", "无")
        ),
        hovertemplate = "%{text}<extra></extra>"
      ) %>%
      layout(
        title = list(
          text = if (!is.null(compound_name)) paste("同位素内标EIC数据 -", compound_name) else "同位素内标EIC数据",
          font = list(size = 16)
        ),
        scene = list(
          xaxis = list(title = "保留时间 (min)"),
          yaxis = list(title = "强度", type = "log"),
          zaxis = list(title = "样本名称"),
          camera = list(
            eye = list(x = 1.5, y = 1.5, z = 1.5)
          )
        ),
        legend = list(
          orientation = "v",
          x = 1.02,
          y = 1
        ),
        margin = list(l = 0, r = 150, b = 0, t = 50)
      )
      
      return(p)
    },
    
    # 准备MS2碎片数据
    prepare_ms2_fragment_data = function(eic_data, max_spectra = NULL) {
      if (is.null(max_spectra)) {
        max_spectra <- max_ms2_spectra
      }
      
      if (is.null(eic_data) || is.null(eic_data$compounds)) {
        return(NULL)
      }
      
      ms2_data_list <- list()
      spectra_count <- 0
      
      for (compound_name in names(eic_data$compounds)) {
        compound_data <- eic_data$compounds[[compound_name]]
        
        if (!is.null(compound_data$ms2_data_by_peak)) {
          for (peak_id in names(compound_data$ms2_data_by_peak)) {
            ms2_spectra <- compound_data$ms2_data_by_peak[[peak_id]]
            
            for (spectrum in ms2_spectra) {
              if (spectra_count >= max_spectra) {
                log_warning(paste("达到最大MS2谱图数量限制:", max_spectra, "，停止加载更多谱图"))
                break
              }
              
              if (!is.null(spectrum$fragments) && length(spectrum$fragments) > 0) {
                spectrum_id <- paste(compound_name, peak_id, spectrum$spectrum_id, sep = "_")
                
                fragment_data <- data.frame(
                  spectrum_id = spectrum_id,
                  compound_name = compound_name,
                  peak_id = peak_id,
                  rtime = spectrum$rtime,
                  precursor_mz = spectrum$precursor_mz,
                  precursor_intensity = spectrum$precursor_intensity,
                  collision_energy = spectrum$collision_energy %||% NA,
                  mz = sapply(spectrum$fragments, function(x) x$mz),
                  intensity = sapply(spectrum$fragments, function(x) x$intensity),
                  stringsAsFactors = FALSE
                )
                
                ms2_data_list[[spectrum_id]] <- fragment_data
                spectra_count <- spectra_count + 1
              }
            }
            
            if (spectra_count >= max_spectra) break
          }
          if (spectra_count >= max_spectra) break
        }
      }
      
      if (length(ms2_data_list) > 0) {
        return(do.call(rbind, ms2_data_list))
      } else {
        return(NULL)
      }
    },
    
    # 创建MS2碎片谱图
    create_ms2_fragment_plot = function(eic_data, spectrum_id = NULL, max_spectra = 10) {
      ms2_data <- prepare_ms2_fragment_data(eic_data, max_spectra)
      
      if (is.null(ms2_data) || nrow(ms2_data) == 0) {
        return(NULL)
      }
      
      # 如果指定了谱图ID，则只显示该谱图
      if (!is.null(spectrum_id)) {
        ms2_data <- ms2_data[ms2_data$spectrum_id == spectrum_id, ]
        if (nrow(ms2_data) == 0) {
          return(NULL)
        }
      }
      
      # 创建子图列表
      subplot_list <- list()
      unique_spectra <- unique(ms2_data$spectrum_id)
      
      # 限制显示的谱图数量以避免性能问题
      if (length(unique_spectra) > max_spectra) {
        unique_spectra <- unique_spectra[1:max_spectra]
        log_warning(paste("限制显示前", max_spectra, "个MS2谱图以优化性能"))
      }
      
      for (i in seq_along(unique_spectra)) {
        spectrum_data <- ms2_data[ms2_data$spectrum_id == unique_spectra[i], ]
        
        # 创建柱状图
        p <- plot_ly(
          data = spectrum_data,
          x = ~mz,
          y = ~intensity,
          type = "bar",
          width = 0.8,
          name = paste("谱图", i),
          text = ~paste(
            "m/z:", round(mz, 4), "<br>",
            "强度:", format(intensity, scientific = TRUE), "<br>",
            "前体离子:", round(precursor_mz[1], 4), "<br>",
            "保留时间:", round(rtime[1], 3), "min<br>",
            "碰撞能:", collision_energy[1], "eV"
          ),
          hovertemplate = "%{text}<extra></extra>"
        ) %>%
        layout(
          title = paste("MS2碎片谱图 -", spectrum_data$compound_name[1]),
          xaxis = list(title = "m/z"),
          yaxis = list(title = "强度"),
          showlegend = FALSE
        )
        
        subplot_list[[i]] <- p
      }
      
      # 如果只有一个谱图，直接返回
      if (length(subplot_list) == 1) {
        return(subplot_list[[1]])
      }
      
      # 创建子图网格
      rows <- ceiling(length(subplot_list) / 2)
      cols <- min(2, length(subplot_list))
      
      combined_plot <- subplot(
        subplot_list,
        nrows = rows,
        shareX = FALSE,
        shareY = FALSE,
        titleX = TRUE,
        titleY = TRUE
      ) %>%
      layout(
        title = list(
          text = paste("MS2碎片谱图概览 (显示", length(subplot_list), "个谱图)"),
          font = list(size = 16)
        ),
        height = 300 * rows
      )
      
      return(combined_plot)
    }
  )
)

# 创建EIC可视化器实例
create_eic_visualizer <- function(max_ms2_spectra = 50, color_palette = "Set3", point_size_range = c(3, 10)) {
  return(EICVisualizer$new(max_ms2_spectra, color_palette, point_size_range))
}
