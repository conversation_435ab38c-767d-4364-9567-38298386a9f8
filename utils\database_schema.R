# 质谱数据库结构设计
# 基于Spectra对象的数据结构，设计用于质控分析的数据库表结构

# 加载必要的包
load_database_packages <- function() {
  required_packages <- c("DBI", "RSQLite", "dplyr", "jsonlite")
  
  for (pkg in required_packages) {
    if (!requireNamespace(pkg, quietly = TRUE)) {
      stop(paste("Required package", pkg, "is not installed. Please install it first."))
    }
  }
  
  library(DBI)
  library(RSQLite)
  library(dplyr)
  library(jsonlite)
}

# 数据库表结构定义
get_database_schema <- function() {
  schema <- list(
    # 数据文件索引表
    data_files = list(
      table_name = "data_files",
      description = "数据文件索引表，记录每个_spectra.rds文件的基本信息，使用数据索引ID作为主键",
      columns = list(
        file_id = "TEXT PRIMARY KEY",  # 使用数据索引ID作为主键
        file_name = "TEXT NOT NULL",
        file_path = "TEXT NOT NULL",
        sample_type = "TEXT",  # QC, STD, BLANK, SAMPLE
        scan_mode = "TEXT",    # positive, negative
        total_spectra = "INTEGER",
        ms1_count = "INTEGER",
        ms2_count = "INTEGER",
        data_index_id = "TEXT",  # 对应数据索引中的ID
        created_time = "DATETIME DEFAULT CURRENT_TIMESTAMP",
        last_updated = "DATETIME DEFAULT CURRENT_TIMESTAMP"
      ),
      indexes = c(
        "CREATE INDEX idx_data_files_sample_type ON data_files(sample_type)",
        "CREATE INDEX idx_data_files_scan_mode ON data_files(scan_mode)",
        "CREATE INDEX idx_data_files_data_index_id ON data_files(data_index_id)"
      )
    ),
    
    # MS1 spectraData表
    ms1_spectra_data = list(
      table_name = "ms1_spectra_data",
      description = "MS1级别的谱图元数据表",
      columns = list(
        spectrum_id = "INTEGER PRIMARY KEY AUTOINCREMENT",
        file_id = "TEXT NOT NULL",  # 改为TEXT类型以匹配数据索引ID
        scan_index = "INTEGER NOT NULL",
        ms_level = "INTEGER NOT NULL DEFAULT 1 CHECK (ms_level = 1)",
        rtime = "REAL",  # 保留时间
        acquisition_num = "INTEGER",
        polarity = "INTEGER CHECK (polarity IN (0, 1))",  # 1为正离子，0为负离子
        peaks_count = "INTEGER CHECK (peaks_count >= 0)",
        tot_ion_current = "REAL CHECK (tot_ion_current >= 0)",
        base_peak_mz = "REAL CHECK (base_peak_mz > 0)",
        base_peak_intensity = "REAL CHECK (base_peak_intensity >= 0)",
        low_mz = "REAL CHECK (low_mz > 0)",
        high_mz = "REAL CHECK (high_mz > 0)",
        injection_time = "REAL",
        scan_window_lower_limit = "REAL",
        scan_window_upper_limit = "REAL",
        centroided = "INTEGER CHECK (centroided IN (0, 1))",  # 0/1 布尔值
        smoothed = "INTEGER CHECK (smoothed IN (0, 1))",    # 0/1 布尔值
        spectrum_id_original = "TEXT",  # 原始谱图ID
        filter_string = "TEXT"
      ),
      constraints = c(
        "FOREIGN KEY (file_id) REFERENCES data_files(file_id) ON DELETE CASCADE"
      ),
      indexes = c(
        "CREATE INDEX idx_ms1_spectra_file_id ON ms1_spectra_data(file_id)",
        "CREATE INDEX idx_ms1_spectra_rtime ON ms1_spectra_data(rtime)",
        "CREATE INDEX idx_ms1_spectra_scan_index ON ms1_spectra_data(scan_index)",
        "CREATE INDEX idx_ms1_spectra_tic ON ms1_spectra_data(tot_ion_current)",
        "CREATE INDEX idx_ms1_spectra_file_rtime ON ms1_spectra_data(file_id, rtime)",
        "CREATE INDEX idx_ms1_spectra_file_scan ON ms1_spectra_data(file_id, scan_index)",
        "CREATE INDEX idx_ms1_spectra_base_peak ON ms1_spectra_data(base_peak_mz, base_peak_intensity)"
      )
    ),
    
    # MS1 peaksData表
    ms1_peaks_data = list(
      table_name = "ms1_peaks_data",
      description = "MS1级别的峰数据表",
      columns = list(
        peak_id = "INTEGER PRIMARY KEY AUTOINCREMENT",
        spectrum_id = "INTEGER NOT NULL",
        mz = "REAL NOT NULL CHECK (mz > 0)",
        intensity = "REAL NOT NULL CHECK (intensity >= 0)"
      ),
      constraints = c(
        "FOREIGN KEY (spectrum_id) REFERENCES ms1_spectra_data(spectrum_id) ON DELETE CASCADE"
      ),
      indexes = c(
        "CREATE INDEX idx_ms1_peaks_spectrum_id ON ms1_peaks_data(spectrum_id)",
        "CREATE INDEX idx_ms1_peaks_mz ON ms1_peaks_data(mz)",
        "CREATE INDEX idx_ms1_peaks_intensity ON ms1_peaks_data(intensity)",
        "CREATE INDEX idx_ms1_peaks_mz_intensity ON ms1_peaks_data(mz, intensity)"
      )
    ),
    
    # MS2 spectraData表
    ms2_spectra_data = list(
      table_name = "ms2_spectra_data",
      description = "MS2级别的谱图元数据表",
      columns = list(
        spectrum_id = "INTEGER PRIMARY KEY AUTOINCREMENT",
        file_id = "TEXT NOT NULL",  # 改为TEXT类型以匹配数据索引ID
        scan_index = "INTEGER NOT NULL",
        ms_level = "INTEGER NOT NULL DEFAULT 2 CHECK (ms_level = 2)",
        rtime = "REAL",  # 保留时间
        acquisition_num = "INTEGER",
        polarity = "INTEGER CHECK (polarity IN (0, 1))",  # 1为正离子，0为负离子
        prec_scan_num = "INTEGER",  # 前体离子扫描编号
        precursor_mz = "REAL CHECK (precursor_mz > 0)",      # 前体离子m/z
        precursor_intensity = "REAL CHECK (precursor_intensity >= 0)", # 前体离子强度
        precursor_charge = "INTEGER", # 前体离子电荷
        collision_energy = "REAL CHECK (collision_energy >= 0)",    # 碰撞能
        ms1_peak_id = "INTEGER",  # MS1峰ID，外键指向ms1_peaks_data表
        isolation_window_lower_mz = "REAL CHECK (isolation_window_lower_mz > 0)",  # 隔离窗口下限
        isolation_window_target_mz = "REAL CHECK (isolation_window_target_mz > 0)", # 隔离窗口中心
        isolation_window_upper_mz = "REAL CHECK (isolation_window_upper_mz > 0)",  # 隔离窗口上限
        peaks_count = "INTEGER CHECK (peaks_count >= 0)",
        tot_ion_current = "REAL CHECK (tot_ion_current >= 0)",
        base_peak_mz = "REAL CHECK (base_peak_mz > 0)",
        base_peak_intensity = "REAL CHECK (base_peak_intensity >= 0)",
        low_mz = "REAL CHECK (low_mz > 0)",
        high_mz = "REAL CHECK (high_mz > 0)",
        injection_time = "REAL",
        scan_window_lower_limit = "REAL",
        scan_window_upper_limit = "REAL",
        centroided = "INTEGER CHECK (centroided IN (0, 1))",  # 0/1 布尔值
        smoothed = "INTEGER CHECK (smoothed IN (0, 1))",    # 0/1 布尔值
        spectrum_id_original = "TEXT",  # 原始谱图ID
        filter_string = "TEXT"
      ),
      constraints = c(
        "FOREIGN KEY (file_id) REFERENCES data_files(file_id) ON DELETE CASCADE",
        "FOREIGN KEY (ms1_peak_id) REFERENCES ms1_peaks_data(peak_id) ON DELETE SET NULL"
      ),
      indexes = c(
        "CREATE INDEX idx_ms2_spectra_file_id ON ms2_spectra_data(file_id)",
        "CREATE INDEX idx_ms2_spectra_rtime ON ms2_spectra_data(rtime)",
        "CREATE INDEX idx_ms2_spectra_scan_index ON ms2_spectra_data(scan_index)",
        "CREATE INDEX idx_ms2_spectra_precursor_mz ON ms2_spectra_data(precursor_mz)",
        "CREATE INDEX idx_ms2_spectra_collision_energy ON ms2_spectra_data(collision_energy)",
        "CREATE INDEX idx_ms2_spectra_prec_scan_num ON ms2_spectra_data(prec_scan_num)",
        "CREATE INDEX idx_ms2_spectra_ms1_peak_id ON ms2_spectra_data(ms1_peak_id)",
        "CREATE INDEX idx_ms2_spectra_file_rtime ON ms2_spectra_data(file_id, rtime)",
        "CREATE INDEX idx_ms2_spectra_file_scan ON ms2_spectra_data(file_id, scan_index)",
        "CREATE INDEX idx_ms2_spectra_precursor_ce ON ms2_spectra_data(precursor_mz, collision_energy)",
        "CREATE INDEX idx_ms2_spectra_file_precursor ON ms2_spectra_data(file_id, precursor_mz)",
        "CREATE INDEX idx_ms2_spectra_file_prec_scan ON ms2_spectra_data(file_id, prec_scan_num)"
      )
    ),
    
    # MS2 peaksData表
    ms2_peaks_data = list(
      table_name = "ms2_peaks_data",
      description = "MS2级别的峰数据表",
      columns = list(
        peak_id = "INTEGER PRIMARY KEY AUTOINCREMENT",
        spectrum_id = "INTEGER NOT NULL",
        mz = "REAL NOT NULL CHECK (mz > 0)",
        intensity = "REAL NOT NULL CHECK (intensity >= 0)"
      ),
      constraints = c(
        "FOREIGN KEY (spectrum_id) REFERENCES ms2_spectra_data(spectrum_id) ON DELETE CASCADE"
      ),
      indexes = c(
        "CREATE INDEX idx_ms2_peaks_spectrum_id ON ms2_peaks_data(spectrum_id)",
        "CREATE INDEX idx_ms2_peaks_mz ON ms2_peaks_data(mz)",
        "CREATE INDEX idx_ms2_peaks_intensity ON ms2_peaks_data(intensity)",
        "CREATE INDEX idx_ms2_peaks_mz_intensity ON ms2_peaks_data(mz, intensity)"
      )
    )
  )
  
  return(schema)
}

# 创建数据库表的SQL语句生成函数
generate_create_table_sql <- function(table_def) {
  table_name <- table_def$table_name
  columns <- table_def$columns
  constraints <- table_def$constraints

  # 构建列定义
  column_defs <- character()
  for (col_name in names(columns)) {
    column_defs <- c(column_defs, paste(col_name, columns[[col_name]]))
  }

  # 合并列定义和约束
  all_defs <- column_defs
  if (!is.null(constraints) && length(constraints) > 0) {
    all_defs <- c(all_defs, constraints)
  }

  # 生成CREATE TABLE语句
  sql <- paste0(
    "CREATE TABLE IF NOT EXISTS ", table_name, " (\n  ",
    paste(all_defs, collapse = ",\n  "),
    "\n);"
  )

  return(sql)
}

# 创建数据库模式（使用现有连接）
create_database_schema <- function(con, schema = NULL) {
  if (is.null(schema)) {
    schema <- get_database_schema()
  }

  # 启用外键约束
  DBI::dbExecute(con, "PRAGMA foreign_keys = ON;")

  # 创建所有表
  for (table_name in names(schema)) {
    table_def <- schema[[table_name]]

    cat("创建表:", table_def$table_name, "\n")

    # 创建表
    create_sql <- generate_create_table_sql(table_def)
    DBI::dbExecute(con, create_sql)

    # 创建索引（安全模式，跳过已存在的索引）
    if (!is.null(table_def$indexes)) {
      for (index_sql in table_def$indexes) {
        tryCatch({
          DBI::dbExecute(con, index_sql)
          cat("索引创建成功:", gsub(".*INDEX\\s+(\\w+)\\s+ON.*", "\\1", index_sql), "\n")
        }, error = function(e) {
          if (grepl("already exists", e$message, ignore.case = TRUE)) {
            index_name <- gsub(".*INDEX\\s+(\\w+)\\s+ON.*", "\\1", index_sql)
            cat("索引已存在，跳过:", index_name, "\n")
          } else {
            cat("索引创建失败:", index_sql, "-", e$message, "\n")
            stop(e)  # 其他错误仍然抛出
          }
        })
      }
    }
  }

  cat("数据库模式创建完成\n")
  return(TRUE)
}

# 创建完整数据库的函数
create_database <- function(db_path, schema = NULL) {
  if (is.null(schema)) {
    schema <- get_database_schema()
  }

  # 连接到数据库
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)

  tryCatch({
    # 使用create_database_schema创建模式
    create_database_schema(con, schema)
    cat("数据库创建完成:", db_path, "\n")

  }, finally = {
    DBI::dbDisconnect(con)
  })

  return(TRUE)
}

# 获取数据库信息的函数
get_database_info <- function(db_path) {
  if (!file.exists(db_path)) {
    return(list(exists = FALSE))
  }
  
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    # 获取所有表名
    tables <- DBI::dbListTables(con)
    
    # 获取每个表的行数
    table_counts <- list()
    for (table in tables) {
      count_sql <- paste("SELECT COUNT(*) as count FROM", table)
      result <- DBI::dbGetQuery(con, count_sql)
      table_counts[[table]] <- result$count[1]
    }
    
    return(list(
      exists = TRUE,
      tables = tables,
      table_counts = table_counts
    ))
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
}

# 验证数据库结构的函数
validate_database_schema <- function(db_path) {
  if (!file.exists(db_path)) {
    return(list(valid = FALSE, message = "数据库文件不存在"))
  }
  
  con <- DBI::dbConnect(RSQLite::SQLite(), db_path)
  
  tryCatch({
    schema <- get_database_schema()
    expected_tables <- names(schema)
    actual_tables <- DBI::dbListTables(con)
    
    missing_tables <- setdiff(expected_tables, actual_tables)
    extra_tables <- setdiff(actual_tables, expected_tables)
    
    if (length(missing_tables) > 0) {
      return(list(
        valid = FALSE,
        message = paste("缺少表:", paste(missing_tables, collapse = ", "))
      ))
    }
    
    return(list(
      valid = TRUE,
      message = "数据库结构验证通过",
      missing_tables = missing_tables,
      extra_tables = extra_tables
    ))
    
  }, finally = {
    DBI::dbDisconnect(con)
  })
}
