# Server层 - 同位素内标EIC数据提取服务
# 负责处理同位素内标离子的EIC数据提取请求

# 加载必要的脚本
safe_source("utils/isotope_eic_extractor.R")
safe_source("utils/logger.R")

# 加载异步处理包
if (!require(future, quietly = TRUE)) {
  install.packages("future")
  library(future)
}

if (!require(promises, quietly = TRUE)) {
  install.packages("promises")
  library(promises)
}

# 设置异步处理
plan(multisession)

# 同位素内标EIC提取状态管理
if (is_shiny_env()) {
  isotope_eic_extraction_status <- reactiveVal("待提取")
  isotope_eic_extraction_progress <- reactiveVal(0)
  isotope_eic_results <- reactiveVal(NULL)
  isotope_eic_summary <- reactiveVal(NULL)
} else {
  # 非Shiny环境的占位符
  isotope_eic_extraction_status <- function() "待提取"
  isotope_eic_extraction_progress <- function() 0
  isotope_eic_results <- function() NULL
  isotope_eic_summary <- function() NULL
}

# 同位素内标EIC提取状态显示
output$isotope_eic_status <- renderText({
  status <- isotope_eic_extraction_status()
  switch(status,
    "待提取" = "准备就绪，等待开始提取",
    "提取中" = paste("正在提取中... (", isotope_eic_extraction_progress(), "%)"),
    "完成" = "EIC数据提取完成",
    "失败" = "EIC数据提取失败",
    status
  )
})

# 提取进度条
output$isotope_eic_progress <- renderUI({
  status <- isotope_eic_extraction_status()
  if (status == "提取中") {
    progress <- isotope_eic_extraction_progress()
    div(
      class = "progress",
      div(
        class = "progress-bar progress-bar-striped progress-bar-animated",
        role = "progressbar",
        style = paste0("width: ", progress, "%"),
        paste0(progress, "%")
      )
    )
  } else {
    NULL
  }
})

# 同位素内标EIC提取摘要
output$isotope_eic_summary <- renderUI({
  summary_data <- isotope_eic_summary()
  if (is.null(summary_data)) {
    return(div(class = "alert alert-info", "暂无提取结果"))
  }
  
  div(
    class = "row",
    div(
      class = "col-md-3",
      div(
        class = "info-box bg-info",
        div(class = "info-box-icon", icon("flask")),
        div(
          class = "info-box-content",
          span(class = "info-box-text", "化合物总数"),
          span(class = "info-box-number", summary_data$total_compounds)
        )
      )
    ),
    div(
      class = "col-md-3",
      div(
        class = "info-box bg-success",
        div(class = "info-box-icon", icon("chart-line")),
        div(
          class = "info-box-content",
          span(class = "info-box-text", "EIC数据点"),
          span(class = "info-box-number", summary_data$total_eic_points)
        )
      )
    ),
    div(
      class = "col-md-3",
      div(
        class = "info-box bg-warning",
        div(class = "info-box-icon", icon("microscope")),
        div(
          class = "info-box-content",
          span(class = "info-box-text", "MS2谱图"),
          span(class = "info-box-number", summary_data$total_ms2_spectra)
        )
      )
    ),
    div(
      class = "col-md-3",
      div(
        class = "info-box bg-primary",
        div(class = "info-box-icon", icon("atom")),
        div(
          class = "info-box-content",
          span(class = "info-box-text", "MS2碎片"),
          span(class = "info-box-number", summary_data$total_ms2_fragments)
        )
      )
    )
  )
})

# 开始同位素内标EIC提取
observeEvent(input$start_isotope_eic_extraction, {
  tryCatch({
    # 验证项目状态
    project_root <- get_project_root_path()
    if (is.null(project_root)) {
      showNotification("请先创建或导入项目", type = "warning")
      return()
    }
    
    # 检查数据库是否存在
    db_path <- file.path(project_root, "data", "spectra.db")
    if (!file.exists(db_path)) {
      showNotification("项目数据库不存在，请先转换原始数据", type = "warning")
      return()
    }
    
    # 检查监控离子配置
    monitor_ions_data_current <- monitor_ions_data()
    if (nrow(monitor_ions_data_current) == 0) {
      showNotification("请先配置监控离子", type = "warning")
      return()
    }
    
    # 过滤出同位素内标离子
    isotope_ions <- monitor_ions_data_current[
      grepl("同位素|内标|isotope|internal", monitor_ions_data_current$化合物名称, ignore.case = TRUE) |
      grepl("同位素|内标|isotope|internal", monitor_ions_data_current$备注, ignore.case = TRUE), 
    ]
    
    if (nrow(isotope_ions) == 0) {
      showNotification("未找到同位素内标离子，请检查监控离子配置", type = "warning")
      return()
    }
    
    # 设置提取状态
    isotope_eic_extraction_status("提取中")
    isotope_eic_extraction_progress(0)
    
    showNotification(paste("开始提取", nrow(isotope_ions), "个同位素内标的EIC数据"), type = "message")
    log_info(paste("开始同位素内标EIC提取，共", nrow(isotope_ions), "个化合物"))
    
    # 获取提取参数
    tolerance_ppm <- input$eic_tolerance_ppm %||% 20
    min_intensity <- input$eic_min_intensity %||% 1000
    
    # 使用简化的同步处理（避免复杂的异步依赖）
    # 在实际生产环境中可以考虑使用异步处理

    # 确保结果目录存在
    results_dir <- file.path(project_root, "results")
    if (!dir.exists(results_dir)) {
      dir.create(results_dir, recursive = TRUE)
    }

    # 执行EIC数据提取
    results <- extract_isotope_eic_from_project(
      isotope_ions,
      output_file = file.path(results_dir, "isotope_eic_data.json"),
      tolerance_ppm = tolerance_ppm,
      min_intensity = min_intensity
    )

    # 处理提取结果
    if (!is.null(results)) {
      # 提取成功
      isotope_eic_results(results)
      isotope_eic_extraction_status("完成")
      isotope_eic_extraction_progress(100)

      # 计算摘要信息
      total_compounds <- length(results$compounds)
      total_eic_points <- sum(sapply(results$compounds, function(x) {
        if (!is.null(x$summary$total_eic_points)) x$summary$total_eic_points else 0
      }))
      total_ms2_spectra <- sum(sapply(results$compounds, function(x) {
        if (!is.null(x$summary$total_ms2_spectra)) x$summary$total_ms2_spectra else 0
      }))
      total_ms2_fragments <- sum(sapply(results$compounds, function(x) {
        if (!is.null(x$summary$total_ms2_fragments)) x$summary$total_ms2_fragments else 0
      }))

      summary_data <- list(
        total_compounds = total_compounds,
        total_eic_points = total_eic_points,
        total_ms2_spectra = total_ms2_spectra,
        total_ms2_fragments = total_ms2_fragments,
        extraction_time = results$metadata$extraction_time
      )

      isotope_eic_summary(summary_data)

      showNotification(
        paste("EIC数据提取完成！共处理", total_compounds, "个化合物，",
              total_eic_points, "个EIC数据点"),
        type = "success",
        duration = 5
      )

      log_info(paste("同位素内标EIC提取完成，结果已保存"))

    } else {
      # 提取失败
      isotope_eic_extraction_status("失败")
      isotope_eic_extraction_progress(0)
      showNotification("EIC数据提取失败，请检查日志", type = "error")
      log_error("同位素内标EIC提取失败")
    }
    
  }, error = function(e) {
    isotope_eic_extraction_status("失败")
    isotope_eic_extraction_progress(0)
    showNotification(paste("启动EIC提取失败:", e$message), type = "error")
    log_error(paste("启动同位素内标EIC提取失败:", e$message))
  })
})

# EIC提取结果表格显示
output$isotope_eic_results_table <- DT::renderDataTable({
  results <- isotope_eic_results()
  
  if (is.null(results) || is.null(results$compounds)) {
    return(data.frame(
      提示 = "暂无EIC提取结果",
      说明 = "请先配置同位素内标离子并开始提取",
      stringsAsFactors = FALSE
    ))
  }
  
  # 构建结果摘要表格
  summary_rows <- list()
  
  for (compound_name in names(results$compounds)) {
    compound_data <- results$compounds[[compound_name]]
    
    if (!is.null(compound_data)) {
      row <- list(
        化合物名称 = compound_name,
        目标质荷比 = compound_data$isotope_info$target_mz,
        离子类型 = compound_data$isotope_info$ion_type,
        涉及文件数 = compound_data$summary$total_files,
        EIC数据点 = compound_data$summary$total_eic_points,
        MS2谱图数 = compound_data$summary$total_ms2_spectra,
        MS2碎片数 = compound_data$summary$total_ms2_fragments
      )
      summary_rows[[length(summary_rows) + 1]] <- row
    }
  }
  
  if (length(summary_rows) > 0) {
    result_df <- do.call(rbind, lapply(summary_rows, data.frame, stringsAsFactors = FALSE))
    return(result_df)
  } else {
    return(data.frame(
      提示 = "EIC提取结果为空",
      说明 = "未找到匹配的同位素内标数据",
      stringsAsFactors = FALSE
    ))
  }
}, options = list(
  pageLength = 10,
  scrollX = TRUE,
  language = list(url = '//cdn.datatables.net/plug-ins/1.10.11/i18n/Chinese.json')
))

# 下载EIC提取结果
output$download_isotope_eic_results <- downloadHandler(
  filename = function() {
    paste("isotope_eic_results_", Sys.Date(), ".json", sep = "")
  },
  content = function(file) {
    results <- isotope_eic_results()
    if (!is.null(results)) {
      json_content <- jsonlite::toJSON(results, auto_unbox = TRUE, pretty = TRUE)
      writeLines(json_content, file, useBytes = TRUE)
    } else {
      writeLines("[]", file)
    }
  }
)
